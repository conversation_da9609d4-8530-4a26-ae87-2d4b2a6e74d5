package post_api

import (
	"context"
	"strings"
	"testing"
	"encore.app/infrastructure"
	"encore.app/types"
)

// Run tests using `encore test`, which compiles the Encore app and then runs `go test`.
// It supports all the same flags that the `go test` command does.
// You automatically get tracing for tests in the local dev dash: http://localhost:9400
// Learn more: https://encore.dev/docs/go/develop/testing

// =============================================================================
// QUEUE & PROCESSING TESTS
// =============================================================================

func TestExtractText(t *testing.T) {
	req := &types.ExtractTextRequest{
		Method: "extracttext",
		RowKey: "test_project_001",
		UserID: "test_user",
	}
	resp, err := ExtractText(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "queued successfully") {
		t.Errorf("Expected message to contain 'queued successfully', got %q", resp.Message)
	}
	if resp.RequestBody == "" {
		t.Error("Expected request body, got empty string")
	}
}

func TestExtractTextMissingRowKey(t *testing.T) {
	req := &types.ExtractTextRequest{
		Method: "extracttext",
		RowKey: "",
		UserID: "test_user",
	}
	_, err := ExtractText(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing row key, got nil")
	}
}

func TestTranscode(t *testing.T) {
	req := &types.TranscodeRequest{
		Method:      "transcode",
		AudioFileID: "test_audio_001",
		UserID:      "test_user",
	}
	resp, err := Transcode(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "queued successfully") {
		t.Errorf("Expected message to contain 'queued successfully', got %q", resp.Message)
	}
	if resp.RequestBody == "" {
		t.Error("Expected request body, got empty string")
	}
}

func TestTranscodeMissingAudioFileID(t *testing.T) {
	req := &types.TranscodeRequest{
		Method:      "transcode",
		AudioFileID: "",
		UserID:      "test_user",
	}
	_, err := Transcode(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing audio file ID, got nil")
	}
}

func TestRequestPickupPack(t *testing.T) {
	req := &types.RequestPickupPackRequest{
		Method:      "requestpickuppack",
		AudioFileID: "test_audio_001",
		PackType:    "full",
		UserID:      "test_user",
	}
	resp, err := RequestPickupPack(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "queued successfully") {
		t.Errorf("Expected message to contain 'queued successfully', got %q", resp.Message)
	}
	if resp.RequestBody == "" {
		t.Error("Expected request body, got empty string")
	}
}

func TestRequestPickupPackDefaultPackType(t *testing.T) {
	req := &types.RequestPickupPackRequest{
		Method:      "requestpickuppack",
		AudioFileID: "test_audio_001",
		PackType:    "invalid",
		UserID:      "test_user",
	}
	resp, err := RequestPickupPack(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	// Should default to partial for invalid pack type
	if !strings.Contains(resp.Message, "Partial pickup pack") {
		t.Errorf("Expected message to contain 'Partial pickup pack', got %q", resp.Message)
	}
}

func TestRequestPickupPackMissingAudioFileID(t *testing.T) {
	req := &types.RequestPickupPackRequest{
		Method:      "requestpickuppack",
		AudioFileID: "",
		PackType:    "full",
		UserID:      "test_user",
	}
	_, err := RequestPickupPack(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing audio file ID, got nil")
	}
}

func TestTranscribe(t *testing.T) {
	req := &types.TranscribeRequest{
		Method:            "transcribe",
		AudioFileID:       "test_audio_001",
		Transcriber:       "whisper",
		UsePostProcessing: true,
		UserID:            "test_user",
	}
	resp, err := Transcribe(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "queued successfully") {
		t.Errorf("Expected message to contain 'queued successfully', got %q", resp.Message)
	}
	if resp.RequestBody == "" {
		t.Error("Expected request body, got empty string")
	}
}

func TestTranscribeMissingAudioFileID(t *testing.T) {
	req := &types.TranscribeRequest{
		Method:      "transcribe",
		AudioFileID: "",
		UserID:      "test_user",
	}
	_, err := Transcribe(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing audio file ID, got nil")
	}
}

func TestAlignTranscription(t *testing.T) {
	req := &types.AlignTranscriptionRequest{
		Method:      "aligntranscription",
		AudioFileID: "test_audio_001",
		UserID:      "test_user",
	}
	resp, err := AlignTranscription(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "queued successfully") {
		t.Errorf("Expected message to contain 'queued successfully', got %q", resp.Message)
	}
	if resp.RequestBody == "" {
		t.Error("Expected request body, got empty string")
	}
}

func TestAlignTranscriptionWithRowKey(t *testing.T) {
	req := &types.AlignTranscriptionRequest{
		Method:      "aligntranscription",
		AudioFileID: "",
		RowKey:      "test_audio_001",
		UserID:      "test_user",
	}
	resp, err := AlignTranscription(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
}

func TestAlignTranscriptionMissingParameters(t *testing.T) {
	req := &types.AlignTranscriptionRequest{
		Method:      "aligntranscription",
		AudioFileID: "",
		RowKey:      "",
		UserID:      "test_user",
	}
	_, err := AlignTranscription(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing parameters, got nil")
	}
}

// =============================================================================
// BULK OPERATIONS TESTS
// =============================================================================

func TestDeleteProjects(t *testing.T) {
	req := &types.DeleteProjectsRequest{
		Method:         "deleteprojects",
		ProjectRowKeys: []string{"project_001", "project_002"},
		UserID:         "test_user",
	}
	resp, err := DeleteProjects(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "deleted successfully") {
		t.Errorf("Expected message to contain 'deleted successfully', got %q", resp.Message)
	}
}

func TestDeleteProjectsMissingProjectKeys(t *testing.T) {
	req := &types.DeleteProjectsRequest{
		Method:         "deleteprojects",
		ProjectRowKeys: []string{},
		UserID:         "test_user",
	}
	_, err := DeleteProjects(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing project keys, got nil")
	}
}

func TestDeleteProjectsMissingUserID(t *testing.T) {
	req := &types.DeleteProjectsRequest{
		Method:         "deleteprojects",
		ProjectRowKeys: []string{"project_001"},
		UserID:         "",
	}
	_, err := DeleteProjects(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing user ID, got nil")
	}
}

func TestDeleteAllProjects(t *testing.T) {
	req := &types.BasePostRequest{
		Method: "deleteallprojects",
		UserID: "test_user",
	}
	resp, err := DeleteAllProjects(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "All projects deleted") {
		t.Errorf("Expected message to contain 'All projects deleted', got %q", resp.Message)
	}
}

// =============================================================================
// PROJECT CRUD TESTS
// =============================================================================

func TestAddProject(t *testing.T) {
	project := &infrastructure.Project{
		PartitionKey:    "test_user",
		RowKey:          "test_project_001",
		Name:            "Test Project",
		Description:     "A test project",
		Status:          "Active",
		AudioFileCount:  0,
		IsTextExtracted: false,
		IsTranscribed:   false,
		IsAligned:       false,
	}

	req := &types.ProjectRequest{
		Method:  "addproject",
		UserID:  "test_user",
		Project: project,
	}
	resp, err := AddProject(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "added successfully") {
		t.Errorf("Expected message to contain 'added successfully', got %q", resp.Message)
	}
	if !resp.Success {
		t.Error("Expected success to be true")
	}
}

func TestAddProjectMissingData(t *testing.T) {
	req := &types.ProjectRequest{
		Method:  "addproject",
		UserID:  "test_user",
		Project: nil,
	}
	_, err := AddProject(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing project data, got nil")
	}
}

func TestSaveProject(t *testing.T) {
	project := &infrastructure.Project{
		PartitionKey:    "test_user",
		RowKey:          "test_project_001",
		Name:            "Updated Test Project",
		Description:     "An updated test project",
		Status:          "In Progress",
		AudioFileCount:  5,
		IsTextExtracted: true,
		IsTranscribed:   false,
		IsAligned:       false,
	}

	req := &types.ProjectRequest{
		Method:  "saveproject",
		UserID:  "test_user",
		Project: project,
	}
	resp, err := SaveProject(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "saved successfully") {
		t.Errorf("Expected message to contain 'saved successfully', got %q", resp.Message)
	}
}

func TestDeleteProject(t *testing.T) {
	req := &types.ProjectRequest{
		Method: "deleteproject",
		UserID: "test_user",
		RowKey: "test_project_001",
	}
	resp, err := DeleteProject(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "deleted successfully") {
		t.Errorf("Expected message to contain 'deleted successfully', got %q", resp.Message)
	}
}

func TestDeleteProjectWithProjectData(t *testing.T) {
	project := &infrastructure.Project{
		RowKey: "test_project_002",
	}

	req := &types.ProjectRequest{
		Method:  "deleteproject",
		UserID:  "test_user",
		Project: project,
	}
	resp, err := DeleteProject(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
}

func TestDeleteProjectMissingRowKey(t *testing.T) {
	req := &types.ProjectRequest{
		Method: "deleteproject",
		UserID: "test_user",
		RowKey: "",
	}
	_, err := DeleteProject(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing row key, got nil")
	}
}

// =============================================================================
// AUDIO FILE CRUD TESTS
// =============================================================================

func TestAddAudioFile(t *testing.T) {
	audioFile := &infrastructure.AudioFile{
		PartitionKey: "test_project_001",
		RowKey:       "test_audio_001",
		FileName:     "test_chapter_01.mp3",
		FilePath:     "/audio/test_chapter_01.mp3",
		Duration:     1800.5,
		FileSize:     25600000,
		Format:       "MP3",
		IsProcessed:  false,
	}

	req := &types.AudioFileRequest{
		Method:    "addaudiofile",
		UserID:    "test_user",
		AudioFile: audioFile,
	}
	resp, err := AddAudioFile(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "added successfully") {
		t.Errorf("Expected message to contain 'added successfully', got %q", resp.Message)
	}
}

func TestAddAudioFileMissingData(t *testing.T) {
	req := &types.AudioFileRequest{
		Method:    "addaudiofile",
		UserID:    "test_user",
		AudioFile: nil,
	}
	_, err := AddAudioFile(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing audio file data, got nil")
	}
}

func TestSaveAudioFile(t *testing.T) {
	audioFile := &infrastructure.AudioFile{
		PartitionKey: "test_project_001",
		RowKey:       "test_audio_001",
		FileName:     "updated_chapter_01.mp3",
		FilePath:     "/audio/updated_chapter_01.mp3",
		Duration:     1850.7,
		FileSize:     26000000,
		Format:       "MP3",
		IsProcessed:  true,
	}

	req := &types.AudioFileRequest{
		Method:    "saveaudiofile",
		UserID:    "test_user",
		AudioFile: audioFile,
	}
	resp, err := SaveAudioFile(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "saved successfully") {
		t.Errorf("Expected message to contain 'saved successfully', got %q", resp.Message)
	}
}

func TestDeleteAudioFile(t *testing.T) {
	req := &types.AudioFileRequest{
		Method:       "deleteaudiofile",
		UserID:       "test_user",
		PartitionKey: "test_project_001",
		RowKey:       "test_audio_001",
	}
	resp, err := DeleteAudioFile(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "deleted successfully") {
		t.Errorf("Expected message to contain 'deleted successfully', got %q", resp.Message)
	}
}

func TestDeleteAudioFileWithAudioFileData(t *testing.T) {
	audioFile := &infrastructure.AudioFile{
		PartitionKey: "test_project_001",
		RowKey:       "test_audio_002",
	}

	req := &types.AudioFileRequest{
		Method:    "deleteaudiofile",
		UserID:    "test_user",
		AudioFile: audioFile,
	}
	resp, err := DeleteAudioFile(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
}

func TestDeleteAudioFileMissingKeys(t *testing.T) {
	req := &types.AudioFileRequest{
		Method:       "deleteaudiofile",
		UserID:       "test_user",
		PartitionKey: "",
		RowKey:       "",
		AudioFile:    nil,
	}
	_, err := DeleteAudioFile(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing keys, got nil")
	}
}

// =============================================================================
// ALIGNMENT ITEM CRUD TESTS
// =============================================================================

func TestAddAlignmentItem(t *testing.T) {
	alignmentItem := &infrastructure.AlignmentItem{
		ID:          "test_align_001",
		AudioFileID: "test_audio_001",
		Category:    "Word",
		StartTime:   0.0,
		EndTime:     2.5,
		Text:        "Hello",
		Confidence:  0.95,
		IsConfirmed: false,
	}

	req := &types.AlignmentItemRequest{
		Method:        "addalignmentitem",
		UserID:        "test_user",
		AlignmentItem: alignmentItem,
	}
	resp, err := AddAlignmentItem(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "added successfully") {
		t.Errorf("Expected message to contain 'added successfully', got %q", resp.Message)
	}
}

func TestAddAlignmentItemMissingData(t *testing.T) {
	req := &types.AlignmentItemRequest{
		Method:        "addalignmentitem",
		UserID:        "test_user",
		AlignmentItem: nil,
	}
	_, err := AddAlignmentItem(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing alignment item data, got nil")
	}
}

func TestSaveAlignmentItem(t *testing.T) {
	alignmentItem := &infrastructure.AlignmentItem{
		ID:          "test_align_001",
		AudioFileID: "test_audio_001",
		Category:    "Word",
		StartTime:   0.0,
		EndTime:     2.8,
		Text:        "Hello",
		Confidence:  0.98,
		IsConfirmed: true,
	}

	req := &types.AlignmentItemRequest{
		Method:        "savealignmentitem",
		UserID:        "test_user",
		AlignmentItem: alignmentItem,
	}
	resp, err := SaveAlignmentItem(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "saved successfully") {
		t.Errorf("Expected message to contain 'saved successfully', got %q", resp.Message)
	}
}

func TestDeleteAlignmentItem(t *testing.T) {
	req := &types.AlignmentItemRequest{
		Method:       "deletealignmentitem",
		UserID:       "test_user",
		PartitionKey: "test_audio_001",
		RowKey:       "test_align_001",
	}
	resp, err := DeleteAlignmentItem(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "deleted successfully") {
		t.Errorf("Expected message to contain 'deleted successfully', got %q", resp.Message)
	}
}

func TestDeleteAlignmentItemWithAlignmentData(t *testing.T) {
	alignmentItem := &infrastructure.AlignmentItem{
		ID:          "test_align_002",
		AudioFileID: "test_audio_001",
	}

	req := &types.AlignmentItemRequest{
		Method:        "deletealignmentitem",
		UserID:        "test_user",
		AlignmentItem: alignmentItem,
	}
	resp, err := DeleteAlignmentItem(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
}

// =============================================================================
// BOOK CRUD TESTS
// =============================================================================

func TestAddBook(t *testing.T) {
	book := &infrastructure.Book{
		RowKey:    "test_book_001",
		Title:     "Test Book",
		Author:    "Test Author",
		ISBN:      "978-0123456789",
		PageCount: 250,
		Language:  "English",
	}

	req := &types.BookRequest{
		Method: "addbook",
		UserID: "test_user",
		Book:   book,
	}
	resp, err := AddBook(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "added successfully") {
		t.Errorf("Expected message to contain 'added successfully', got %q", resp.Message)
	}
}

func TestAddBookMissingData(t *testing.T) {
	req := &types.BookRequest{
		Method: "addbook",
		UserID: "test_user",
		Book:   nil,
	}
	_, err := AddBook(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing book data, got nil")
	}
}

func TestSaveBook(t *testing.T) {
	book := &infrastructure.Book{
		RowKey:    "test_book_001",
		Title:     "Updated Test Book",
		Author:    "Updated Test Author",
		ISBN:      "978-0123456789",
		PageCount: 300,
		Language:  "English",
	}

	req := &types.BookRequest{
		Method: "savebook",
		UserID: "test_user",
		Book:   book,
	}
	resp, err := SaveBook(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "saved successfully") {
		t.Errorf("Expected message to contain 'saved successfully', got %q", resp.Message)
	}
}

func TestDeleteBook(t *testing.T) {
	req := &types.BookRequest{
		Method:       "deletebook",
		UserID:       "test_user",
		PartitionKey: "test_book_001",
		RowKey:       "test_book_001",
	}
	resp, err := DeleteBook(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "deleted successfully") {
		t.Errorf("Expected message to contain 'deleted successfully', got %q", resp.Message)
	}
}

// =============================================================================
// USER CRUD TESTS
// =============================================================================

func TestAddUser(t *testing.T) {
	user := &infrastructure.TrackSurferUser{
		PartitionKey: "test_user_partition",
		RowKey:       "test_user_001",
		Name:         "Test User",
		Email:        "<EMAIL>",
		IsActive:     true,
		Role:         "User",
	}

	req := &types.UserRequest{
		Method: "adduser",
		UserID: "admin_user",
		User:   user,
	}
	resp, err := AddUser(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "added successfully") {
		t.Errorf("Expected message to contain 'added successfully', got %q", resp.Message)
	}
}

func TestAddUserMissingData(t *testing.T) {
	req := &types.UserRequest{
		Method: "adduser",
		UserID: "admin_user",
		User:   nil,
	}
	_, err := AddUser(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing user data, got nil")
	}
}

func TestSaveUser(t *testing.T) {
	user := &infrastructure.TrackSurferUser{
		PartitionKey: "test_user_partition",
		RowKey:       "test_user_001",
		Name:         "Updated Test User",
		Email:        "<EMAIL>",
		IsActive:     true,
		Role:         "Admin",
	}

	req := &types.UserRequest{
		Method: "saveuser",
		UserID: "admin_user",
		User:   user,
	}
	resp, err := SaveUser(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "saved successfully") {
		t.Errorf("Expected message to contain 'saved successfully', got %q", resp.Message)
	}
}

func TestDeleteUser(t *testing.T) {
	req := &types.UserRequest{
		Method:       "deleteuser",
		UserID:       "admin_user",
		PartitionKey: "test_user_partition",
		RowKey:       "test_user_001",
	}
	resp, err := DeleteUser(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
	if !strings.Contains(resp.Message, "deleted successfully") {
		t.Errorf("Expected message to contain 'deleted successfully', got %q", resp.Message)
	}
}

func TestDeleteUserWithUserData(t *testing.T) {
	user := &infrastructure.TrackSurferUser{
		PartitionKey: "test_user_partition",
		RowKey:       "test_user_002",
	}

	req := &types.UserRequest{
		Method: "deleteuser",
		UserID: "admin_user",
		User:   user,
	}
	resp, err := DeleteUser(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected message, got empty string")
	}
}

// =============================================================================
// INTEGRATION TESTS
// =============================================================================

func TestCompleteProjectWorkflow(t *testing.T) {
	// Test a complete workflow: Add Project -> Add Audio File -> Process -> Delete

	// 1. Add a project
	project := &infrastructure.Project{
		PartitionKey:    "test_user",
		RowKey:          "workflow_project_001",
		Name:            "Workflow Test Project",
		Description:     "A project for testing the complete workflow",
		Status:          "Active",
		AudioFileCount:  0,
		IsTextExtracted: false,
		IsTranscribed:   false,
		IsAligned:       false,
	}

	addProjectReq := &types.ProjectRequest{
		Method:  "addproject",
		UserID:  "test_user",
		Project: project,
	}
	addProjectResp, err := AddProject(context.Background(), addProjectReq)
	if err != nil {
		t.Fatal("Failed to add project:", err)
	}
	t.Logf("Project added: %s", addProjectResp.Message)

	// 2. Add an audio file to the project
	audioFile := &infrastructure.AudioFile{
		PartitionKey: project.RowKey,
		RowKey:       "workflow_audio_001",
		FileName:     "workflow_test.mp3",
		FilePath:     "/audio/workflow_test.mp3",
		Duration:     1200.0,
		FileSize:     15000000,
		Format:       "MP3",
		IsProcessed:  false,
	}

	addAudioReq := &types.AudioFileRequest{
		Method:    "addaudiofile",
		UserID:    "test_user",
		AudioFile: audioFile,
	}
	addAudioResp, err := AddAudioFile(context.Background(), addAudioReq)
	if err != nil {
		t.Fatal("Failed to add audio file:", err)
	}
	t.Logf("Audio file added: %s", addAudioResp.Message)

	// 3. Queue text extraction
	extractReq := &types.ExtractTextRequest{
		Method: "extracttext",
		RowKey: project.RowKey,
		UserID: "test_user",
	}
	extractResp, err := ExtractText(context.Background(), extractReq)
	if err != nil {
		t.Fatal("Failed to queue text extraction:", err)
	}
	t.Logf("Text extraction queued: %s", extractResp.Message)

	// 4. Queue transcoding
	transcodeReq := &types.TranscodeRequest{
		Method:      "transcode",
		AudioFileID: audioFile.RowKey,
		UserID:      "test_user",
	}
	transcodeResp, err := Transcode(context.Background(), transcodeReq)
	if err != nil {
		t.Fatal("Failed to queue transcoding:", err)
	}
	t.Logf("Transcoding queued: %s", transcodeResp.Message)

	// 5. Queue transcription
	transcribeReq := &types.TranscribeRequest{
		Method:      "transcribe",
		AudioFileID: audioFile.RowKey,
		UserID:      "test_user",
	}
	transcribeResp, err := Transcribe(context.Background(), transcribeReq)
	if err != nil {
		t.Fatal("Failed to queue transcription:", err)
	}
	t.Logf("Transcription queued: %s", transcribeResp.Message)

	// 6. Queue alignment
	alignReq := &types.AlignTranscriptionRequest{
		Method:      "aligntranscription",
		AudioFileID: audioFile.RowKey,
		UserID:      "test_user",
	}
	alignResp, err := AlignTranscription(context.Background(), alignReq)
	if err != nil {
		t.Fatal("Failed to queue alignment:", err)
	}
	t.Logf("Alignment queued: %s", alignResp.Message)

	// 7. Clean up - delete the project
	deleteProjectReq := &types.ProjectRequest{
		Method: "deleteproject",
		UserID: "test_user",
		RowKey: project.RowKey,
	}
	deleteProjectResp, err := DeleteProject(context.Background(), deleteProjectReq)
	if err != nil {
		t.Fatal("Failed to delete project:", err)
	}
	t.Logf("Project deleted: %s", deleteProjectResp.Message)

	t.Log("Complete workflow test passed successfully")
}

func TestAllQueueEndpointsReturnValidResponse(t *testing.T) {
	tests := []struct {
		name string
		fn   func() (*types.QueueResponse, error)
	}{
		{
			"ExtractText",
			func() (*types.QueueResponse, error) {
				req := &types.ExtractTextRequest{Method: "extracttext", RowKey: "test_project", UserID: "test_user"}
				return ExtractText(context.Background(), req)
			},
		},
		{
			"Transcode",
			func() (*types.QueueResponse, error) {
				req := &types.TranscodeRequest{Method: "transcode", AudioFileID: "test_audio", UserID: "test_user"}
				return Transcode(context.Background(), req)
			},
		},
		{
			"RequestPickupPack",
			func() (*types.QueueResponse, error) {
				req := &types.RequestPickupPackRequest{Method: "requestpickuppack", AudioFileID: "test_audio", PackType: "full", UserID: "test_user"}
				return RequestPickupPack(context.Background(), req)
			},
		},
		{
			"Transcribe",
			func() (*types.QueueResponse, error) {
				req := &types.TranscribeRequest{Method: "transcribe", AudioFileID: "test_audio", UserID: "test_user"}
				return Transcribe(context.Background(), req)
			},
		},
		{
			"AlignTranscription",
			func() (*types.QueueResponse, error) {
				req := &types.AlignTranscriptionRequest{Method: "aligntranscription", AudioFileID: "test_audio", UserID: "test_user"}
				return AlignTranscription(context.Background(), req)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := tt.fn()
			if err != nil {
				t.Fatal(err)
			}
			if resp.Message == "" {
				t.Error("Expected non-empty message")
			}
			if !strings.Contains(resp.Message, "queued successfully") {
				t.Errorf("Expected message to contain 'queued successfully', got %q", resp.Message)
			}
			if resp.RequestBody == "" {
				t.Error("Expected non-empty request body")
			}
		})
	}
}

func TestAllCRUDEndpointsReturnValidResponse(t *testing.T) {
	// Test that all CRUD endpoints return proper success responses

	// Project CRUD
	project := &infrastructure.Project{PartitionKey: "test", RowKey: "test", Name: "Test", Status: "Active"}
	projectReq := &types.ProjectRequest{Method: "addproject", UserID: "test", Project: project}
	projectResp, err := AddProject(context.Background(), projectReq)
	if err != nil {
		t.Fatal("AddProject failed:", err)
	}
	if !projectResp.Success {
		t.Error("Expected AddProject success to be true")
	}

	// Audio File CRUD
	audioFile := &infrastructure.AudioFile{PartitionKey: "test", RowKey: "test", FileName: "test.mp3", Duration: 100, FileSize: 1000, Format: "MP3"}
	audioReq := &types.AudioFileRequest{Method: "addaudiofile", UserID: "test", AudioFile: audioFile}
	audioResp, err := AddAudioFile(context.Background(), audioReq)
	if err != nil {
		t.Fatal("AddAudioFile failed:", err)
	}
	if audioResp.Message == "" {
		t.Error("Expected AddAudioFile message to be non-empty")
	}

	// Alignment Item CRUD
	alignmentItem := &infrastructure.AlignmentItem{ID: "test", AudioFileID: "test", Category: "Word", StartTime: 0, EndTime: 1, Text: "test", Confidence: 0.9}
	alignmentReq := &types.AlignmentItemRequest{Method: "addalignmentitem", UserID: "test", AlignmentItem: alignmentItem}
	alignmentResp, err := AddAlignmentItem(context.Background(), alignmentReq)
	if err != nil {
		t.Fatal("AddAlignmentItem failed:", err)
	}
	if alignmentResp.Message == "" {
		t.Error("Expected AddAlignmentItem message to be non-empty")
	}

	// Book CRUD
	book := &infrastructure.Book{RowKey: "test", Title: "Test Book", Author: "Test Author"}
	bookReq := &types.BookRequest{Method: "addbook", UserID: "test", Book: book}
	bookResp, err := AddBook(context.Background(), bookReq)
	if err != nil {
		t.Fatal("AddBook failed:", err)
	}
	if bookResp.Message == "" {
		t.Error("Expected AddBook message to be non-empty")
	}

	// User CRUD
	user := &infrastructure.TrackSurferUser{PartitionKey: "test", RowKey: "test", Name: "Test User", Email: "<EMAIL>", IsActive: true}
	userReq := &types.UserRequest{Method: "adduser", UserID: "admin", User: user}
	userResp, err := AddUser(context.Background(), userReq)
	if err != nil {
		t.Fatal("AddUser failed:", err)
	}
	if userResp.Message == "" {
		t.Error("Expected AddUser message to be non-empty")
	}
}
