// Package post_api provides POST API endpoints for TrackSurfer application
package post_api

import (
	"context"
	"encore.app/infrastructure"
	"encore.app/types"
	"encore.dev/beta/errs"
	"fmt"
)

// =============================================================================
// QUEUE AND PROCESSING ENDPOINTS
// =============================================================================

// ExtractText queues text extraction request
//
//encore:api public method=POST path=/api/track-surfer/extracttext
func ExtractText(ctx context.Context, req *types.ExtractTextRequest) (*types.QueueResponse, error) {
	if req.RowKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing rowKey parameter",
		}
	}

	// Call database layer to queue text extraction
	message, requestBody, err := infrastructure.DbUtils.QueueTextExtraction(req.RowKey, req.Method)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue text extraction: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// Transcode queues audio transcoding request
//
//encore:api public method=POST path=/api/track-surfer/transcode
func Transcode(ctx context.Context, req *types.TranscodeRequest) (*types.QueueResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Call database layer to queue transcoding
	message, requestBody, err := infrastructure.DbUtils.QueueTranscoding(req.AudioFileID, req.Method)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue transcoding: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// RequestPickupPack queues pickup pack creation request
//
//encore:api public method=POST path=/api/track-surfer/requestpickuppack
func RequestPickupPack(ctx context.Context, req *types.RequestPickupPackRequest) (*types.QueueResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Validate pack type
	if req.PackType != "full" && req.PackType != "partial" {
		req.PackType = "partial" // Default to partial
	}

	// Call database layer to queue pickup pack creation
	message, requestBody, err := infrastructure.DbUtils.QueuePickupPackCreation(req.AudioFileID, req.PackType)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue pickup pack creation: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// Transcribe queues transcription request
//
//encore:api public method=POST path=/api/track-surfer/transcribe
func Transcribe(ctx context.Context, req *types.TranscribeRequest) (*types.QueueResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Call database layer to queue transcription
	message, requestBody, err := infrastructure.DbUtils.QueueTranscription(req.AudioFileID, req.Transcriber, req.UsePostProcessing, req.UserID)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue transcription: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// AlignTranscription queues transcription alignment request
//
//encore:api public method=POST path=/api/track-surfer/aligntranscription
func AlignTranscription(ctx context.Context, req *types.AlignTranscriptionRequest) (*types.QueueResponse, error) {
	audioFileID := req.AudioFileID
	if audioFileID == "" && req.RowKey != "" {
		audioFileID = req.RowKey
	}

	if audioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID or rowKey parameter",
		}
	}

	// Call database layer to queue alignment
	message, requestBody, err := infrastructure.DbUtils.QueueAlignment(audioFileID)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue alignment: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// =============================================================================
// BULK OPERATIONS
// =============================================================================

// DeleteProjects handles bulk project deletion
//
//encore:api public method=POST path=/api/track-surfer/deleteprojects
func DeleteProjects(ctx context.Context, req *types.DeleteProjectsRequest) (*types.PostResponse, error) {
	if len(req.ProjectRowKeys) == 0 {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "No project row keys provided",
		}
	}

	if req.UserID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing userID parameter",
		}
	}

	// Call database layer to delete projects
	err := infrastructure.DbUtils.DeleteMultipleProjects(req.ProjectRowKeys, req.UserID)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete projects: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Projects deleted successfully",
	}, nil
}

// DeleteAllProjects handles deletion of all projects
//
//encore:api public method=POST path=/api/track-surfer/deleteallprojects
func DeleteAllProjects(ctx context.Context, req *types.BasePostRequest) (*types.PostResponse, error) {
	// Call database layer to delete all projects
	err := infrastructure.DbUtils.DeleteAllProjects()
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete all projects: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "All projects deleted",
	}, nil
}

// =============================================================================
// PROJECT CRUD OPERATIONS
// =============================================================================

// AddProject creates a new project
//
//encore:api public method=POST path=/api/track-surfer/addproject
func AddProject(ctx context.Context, req *types.ProjectRequest) (*types.PostResponse, error) {
	if req.Project == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project data",
		}
	}

	// Call database layer to save project
	err := infrastructure.DbUtils.SaveProject(req.Project)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to add project: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Project added successfully",
		Success: true,
	}, nil
}

// SaveProject updates an existing project
//
//encore:api public method=POST path=/api/track-surfer/saveproject
func SaveProject(ctx context.Context, req *types.ProjectRequest) (*types.PostResponse, error) {
	if req.Project == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project data",
		}
	}

	// Call database layer to save project
	err := infrastructure.DbUtils.SaveProject(req.Project)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to save project: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Project saved successfully",
		Success: true,
	}, nil
}

// DeleteProject deletes a project and all associated data
//
//encore:api public method=POST path=/api/track-surfer/deleteproject
func DeleteProject(ctx context.Context, req *types.ProjectRequest) (*types.PostResponse, error) {
	if req.RowKey == "" && (req.Project == nil || req.Project.RowKey == "") {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project rowKey",
		}
	}

	rowKey := req.RowKey
	if rowKey == "" {
		rowKey = req.Project.RowKey
	}

	// Call database layer to delete project and all associated data
	err := infrastructure.DbUtils.DeleteProjectAndData(rowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete project: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Project deleted successfully",
	}, nil
}

// =============================================================================
// AUDIO FILE CRUD OPERATIONS
// =============================================================================

// AddAudioFile creates a new audio file
//
//encore:api public method=POST path=/api/track-surfer/addaudiofile
func AddAudioFile(ctx context.Context, req *types.AudioFileRequest) (*types.PostResponse, error) {
	if req.AudioFile == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audio file data",
		}
	}

	// Call database layer to save audio file
	err := infrastructure.DbUtils.SaveAudioFile(req.AudioFile)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to add audio file: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Audio file added successfully",
		Success: true,
	}, nil
}

// SaveAudioFile updates an existing audio file
//
//encore:api public method=POST path=/api/track-surfer/saveaudiofile
func SaveAudioFile(ctx context.Context, req *types.AudioFileRequest) (*types.PostResponse, error) {
	if req.AudioFile == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audio file data",
		}
	}

	// Call database layer to save audio file
	err := infrastructure.DbUtils.SaveAudioFile(req.AudioFile)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to save audio file: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Audio file saved successfully",
		Success: true,
	}, nil
}

// DeleteAudioFile deletes an audio file and associated data
//
//encore:api public method=POST path=/api/track-surfer/deleteaudiofile
func DeleteAudioFile(ctx context.Context, req *types.AudioFileRequest) (*types.PostResponse, error) {
	if req.PartitionKey == "" || req.RowKey == "" {
		if req.AudioFile == nil || req.AudioFile.PartitionKey == "" || req.AudioFile.RowKey == "" {
			return nil, &errs.Error{
				Code:    errs.InvalidArgument,
				Message: "Missing audio file partitionKey or rowKey",
			}
		}
		req.PartitionKey = req.AudioFile.PartitionKey
		req.RowKey = req.AudioFile.RowKey
	}

	// Call database layer to delete audio file and associated data
	err := infrastructure.DbUtils.DeleteAudioFileAndData(req.PartitionKey, req.RowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete audio file: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Audio file deleted successfully",
	}, nil
}

// =============================================================================
// ALIGNMENT ITEM CRUD OPERATIONS
// =============================================================================

// AddAlignmentItem creates a new alignment item
//
//encore:api public method=POST path=/api/track-surfer/addalignmentitem
func AddAlignmentItem(ctx context.Context, req *types.AlignmentItemRequest) (*types.PostResponse, error) {
	if req.AlignmentItem == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing alignment item data",
		}
	}

	// Call database layer to save alignment item
	err := infrastructure.DbUtils.SaveAlignmentItem(req.AlignmentItem)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to add alignment item: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Alignment item added successfully",
		Success: true,
	}, nil
}

// SaveAlignmentItem updates an existing alignment item
//
//encore:api public method=POST path=/api/track-surfer/savealignmentitem
func SaveAlignmentItem(ctx context.Context, req *types.AlignmentItemRequest) (*types.PostResponse, error) {
	if req.AlignmentItem == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing alignment item data",
		}
	}

	// Call database layer to save alignment item
	err := infrastructure.DbUtils.SaveAlignmentItem(req.AlignmentItem)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to save alignment item: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Alignment item saved successfully",
		Success: true,
	}, nil
}

// DeleteAlignmentItem deletes an alignment item
//
//encore:api public method=POST path=/api/track-surfer/deletealignmentitem
func DeleteAlignmentItem(ctx context.Context, req *types.AlignmentItemRequest) (*types.PostResponse, error) {
	if req.PartitionKey == "" || req.RowKey == "" {
		if req.AlignmentItem == nil || req.AlignmentItem.ID == "" {
			return nil, &errs.Error{
				Code:    errs.InvalidArgument,
				Message: "Missing alignment item partitionKey or rowKey",
			}
		}
		// Extract keys from alignment item if not provided directly
		req.PartitionKey = req.AlignmentItem.AudioFileID // Assuming AudioFileID is the partition key
		req.RowKey = req.AlignmentItem.ID
	}

	// Call database layer to delete alignment item
	err := infrastructure.DbUtils.DeleteAlignmentItem(req.PartitionKey, req.RowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete alignment item: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Alignment item deleted successfully",
	}, nil
}

// =============================================================================
// BOOK CRUD OPERATIONS
// =============================================================================

// AddBook creates a new book
//
//encore:api public method=POST path=/api/track-surfer/addbook
func AddBook(ctx context.Context, req *types.BookRequest) (*types.PostResponse, error) {
	if req.Book == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing book data",
		}
	}

	// Call database layer to save book
	err := infrastructure.DbUtils.SaveBook(req.Book)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to add book: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Book added successfully",
		Success: true,
	}, nil
}

// SaveBook updates an existing book
//
//encore:api public method=POST path=/api/track-surfer/savebook
func SaveBook(ctx context.Context, req *types.BookRequest) (*types.PostResponse, error) {
	if req.Book == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing book data",
		}
	}

	// Call database layer to save book
	err := infrastructure.DbUtils.SaveBook(req.Book)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to save book: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Book saved successfully",
		Success: true,
	}, nil
}

// DeleteBook deletes a book
//
//encore:api public method=POST path=/api/track-surfer/deletebook
func DeleteBook(ctx context.Context, req *types.BookRequest) (*types.PostResponse, error) {
	if req.PartitionKey == "" || req.RowKey == "" {
		if req.Book == nil || req.Book.RowKey == "" {
			return nil, &errs.Error{
				Code:    errs.InvalidArgument,
				Message: "Missing book partitionKey or rowKey",
			}
		}
		req.PartitionKey = req.Book.RowKey // For books, PartitionKey might be the same as RowKey
		req.RowKey = req.Book.RowKey
	}

	// Call database layer to delete book
	err := infrastructure.DbUtils.DeleteBook(req.PartitionKey, req.RowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete book: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Book deleted successfully",
	}, nil
}

// =============================================================================
// USER CRUD OPERATIONS
// =============================================================================

// AddUser creates a new user
//
//encore:api public method=POST path=/api/track-surfer/adduser
func AddUser(ctx context.Context, req *types.UserRequest) (*types.PostResponse, error) {
	if req.User == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing user data",
		}
	}

	// Call database layer to save user
	err := infrastructure.DbUtils.SaveUser(req.User)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to add user: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "User added successfully",
		Success: true,
	}, nil
}

// SaveUser updates an existing user
//
//encore:api public method=POST path=/api/track-surfer/saveuser
func SaveUser(ctx context.Context, req *types.UserRequest) (*types.PostResponse, error) {
	if req.User == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing user data",
		}
	}

	// Call database layer to save user
	err := infrastructure.DbUtils.SaveUser(req.User)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to save user: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "User saved successfully",
		Success: true,
	}, nil
}

// DeleteUser deletes a user
//
//encore:api public method=POST path=/api/track-surfer/deleteuser
func DeleteUser(ctx context.Context, req *types.UserRequest) (*types.PostResponse, error) {
	if req.PartitionKey == "" || req.RowKey == "" {
		if req.User == nil || req.User.PartitionKey == "" || req.User.RowKey == "" {
			return nil, &errs.Error{
				Code:    errs.InvalidArgument,
				Message: "Missing user partitionKey or rowKey",
			}
		}
		req.PartitionKey = req.User.PartitionKey
		req.RowKey = req.User.RowKey
	}

	// Call database layer to delete user
	err := infrastructure.DbUtils.DeleteUser(req.PartitionKey, req.RowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete user: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "User deleted successfully",
	}, nil
}
