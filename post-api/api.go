// Package post_api provides POST API endpoints for TrackSurfer application
package post_api

import (
	"context"
	"encore.app/infrastructure"
	"encore.app/types"
	"encore.dev/beta/errs"
	"fmt"
)

// =============================================================================
// QUEUE AND PROCESSING ENDPOINTS
// =============================================================================

// ExtractText queues text extraction request
//
//encore:api public method=POST path=/api/track-surfer/extracttext
func ExtractText(ctx context.Context, req *types.ExtractTextRequest) (*types.QueueResponse, error) {
	if req.RowKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing rowKey parameter",
		}
	}

	// Call database layer to queue text extraction
	message, requestBody, err := infrastructure.DbUtils.QueueTextExtraction(req.RowKey, req.Method)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue text extraction: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// Transcode queues audio transcoding request
//
//encore:api public method=POST path=/api/track-surfer/transcode
func Transcode(ctx context.Context, req *types.TranscodeRequest) (*types.QueueResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Call database layer to queue transcoding
	message, requestBody, err := infrastructure.DbUtils.QueueTranscoding(req.AudioFileID, req.Method)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue transcoding: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// RequestPickupPack queues pickup pack creation request
//
//encore:api public method=POST path=/api/track-surfer/requestpickuppack
func RequestPickupPack(ctx context.Context, req *types.RequestPickupPackRequest) (*types.QueueResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Validate pack type
	if req.PackType != "full" && req.PackType != "partial" {
		req.PackType = "partial" // Default to partial
	}

	// Call database layer to queue pickup pack creation
	message, requestBody, err := infrastructure.DbUtils.QueuePickupPackCreation(req.AudioFileID, req.PackType)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue pickup pack creation: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// Transcribe queues transcription request
//
//encore:api public method=POST path=/api/track-surfer/transcribe
func Transcribe(ctx context.Context, req *types.TranscribeRequest) (*types.QueueResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Call database layer to queue transcription
	message, requestBody, err := infrastructure.DbUtils.QueueTranscription(req.AudioFileID, req.Transcriber, req.UsePostProcessing, req.UserID)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue transcription: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// AlignTranscription queues transcription alignment request
//
//encore:api public method=POST path=/api/track-surfer/aligntranscription
func AlignTranscription(ctx context.Context, req *types.AlignTranscriptionRequest) (*types.QueueResponse, error) {
	audioFileID := req.AudioFileID
	if audioFileID == "" && req.RowKey != "" {
		audioFileID = req.RowKey
	}

	if audioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID or rowKey parameter",
		}
	}

	// Call database layer to queue alignment
	message, requestBody, err := infrastructure.DbUtils.QueueAlignment(audioFileID)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to queue alignment: %v", err),
		}
	}

	return &types.QueueResponse{
		Message:     message,
		RequestBody: requestBody,
	}, nil
}

// =============================================================================
// BULK OPERATIONS
// =============================================================================

// DeleteProjects handles bulk project deletion
//
//encore:api public method=POST path=/api/track-surfer/deleteprojects
func DeleteProjects(ctx context.Context, req *types.DeleteProjectsRequest) (*types.PostResponse, error) {
	if len(req.ProjectRowKeys) == 0 {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "No project row keys provided",
		}
	}

	if req.UserID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing userID parameter",
		}
	}

	// Call database layer to delete projects
	err := infrastructure.DbUtils.DeleteMultipleProjects(req.ProjectRowKeys, req.UserID)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete projects: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Projects deleted successfully",
	}, nil
}

// DeleteAllProjects handles deletion of all projects
//
//encore:api public method=POST path=/api/track-surfer/deleteallprojects
func DeleteAllProjects(ctx context.Context, req *types.BasePostRequest) (*types.PostResponse, error) {
	// Call database layer to delete all projects
	err := infrastructure.DbUtils.DeleteAllProjects()
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete all projects: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "All projects deleted",
	}, nil
}

// =============================================================================
// PROJECT CRUD OPERATIONS
// =============================================================================

// AddProject creates a new project
//
//encore:api public method=POST path=/api/track-surfer/addproject
func AddProject(ctx context.Context, req *types.ProjectRequest) (*types.PostResponse, error) {
	if req.Project == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project data",
		}
	}

	// Call database layer to save project
	err := infrastructure.DbUtils.SaveProject(req.Project)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to add project: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Project added successfully",
		Success: true,
	}, nil
}

// SaveProject updates an existing project
//
//encore:api public method=POST path=/api/track-surfer/saveproject
func SaveProject(ctx context.Context, req *types.ProjectRequest) (*types.PostResponse, error) {
	if req.Project == nil {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project data",
		}
	}

	// Call database layer to save project
	err := infrastructure.DbUtils.SaveProject(req.Project)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to save project: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Project saved successfully",
		Data:    req.Project,
	}, nil
}

// DeleteProject deletes a project and all associated data
//
//encore:api public method=POST path=/api/track-surfer/deleteproject
func DeleteProject(ctx context.Context, req *types.ProjectRequest) (*types.PostResponse, error) {
	if req.RowKey == "" && (req.Project == nil || req.Project.RowKey == "") {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project rowKey",
		}
	}

	rowKey := req.RowKey
	if rowKey == "" {
		rowKey = req.Project.RowKey
	}

	// Call database layer to delete project and all associated data
	err := infrastructure.DbUtils.DeleteProjectAndData(rowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete project: %v", err),
		}
	}

	return &types.PostResponse{
		Message: "Project deleted successfully",
	}, nil
}
