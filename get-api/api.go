// Package get_api provides API endpoints for TrackSurfer application
package get_api

import (
	"context"
	"fmt"
	"encore.dev/beta/errs"
	"encore.app/infrastructure"
	"encore.app/types"
)

// User Management Endpoints

// GetUser retrieves a specific user by partition key
//
//encore:api public method=GET path=/api/track-surfer/getuser
func GetUser(ctx context.Context, req *GetUserRequest) (*UserResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing user's partitionKey parameter",
		}
	}

	// Call database layer to get user
	user, err := dbUtils.GetUser(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve user: %v", err),
		}
	}

	return &UserResponse{User: user}, nil
}

// GetUsers retrieves all users
//
//encore:api public method=GET path=/api/track-surfer/getusers
func GetUsers(ctx context.Context) (*UsersResponse, error) {
	// Call database layer to get all users
	users, err := dbUtils.GetAllUsers()
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve users: %v", err),
		}
	}

	return &UsersResponse{Users: users}, nil
}

// Project Management Endpoints

// GetAllProjects retrieves all projects
//
//encore:api public method=GET path=/api/track-surfer/getallprojects
func GetAllProjects(ctx context.Context) (*ProjectsResponse, error) {
	// Call database layer to get all projects
	projects, err := dbUtils.GetAllProjects()
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve projects: %v", err),
		}
	}

	return &ProjectsResponse{Projects: projects}, nil
}

// GetProject retrieves a specific project by row key
//
//encore:api public method=GET path=/api/track-surfer/getproject
func GetProject(ctx context.Context, req *GetProjectRequest) (*ProjectResponse, error) {
	if req.RowKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project's rowKey parameter",
		}
	}

	// Call database layer to get project
	project, err := dbUtils.GetProject(req.RowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve project: %v", err),
		}
	}

	return &ProjectResponse{Project: project}, nil
}

// GetBook retrieves book information by row key
//
//encore:api public method=GET path=/api/track-surfer/getbook
func GetBook(ctx context.Context, req *GetBookRequest) (*BookResponse, error) {
	if req.RowKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project's rowKey parameter",
		}
	}

	// Call database layer to get book
	book, err := dbUtils.GetBook(req.RowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve book: %v", err),
		}
	}

	return &BookResponse{Book: book}, nil
}

// Audio File Management Endpoints

// GetCountOfProjectAudioFiles gets the count of audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getcountofprojectaudiofiles
func GetCountOfProjectAudioFiles(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get count
	count, err := dbUtils.GetCountOfProjectAudioFiles(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get audio file count: %v", err),
		}
	}

	return &CountResponse{Count: count}, nil
}

// GetListOfProjectAudioFiles gets the list of audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getlistofprojectaudiofiles
func GetListOfProjectAudioFiles(ctx context.Context, req *GetListRequest) (*AudioFileListResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get file list
	fileNames, err := dbUtils.GetListOfProjectAudioFiles(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get audio file list: %v", err),
		}
	}

	return &AudioFileListResponse{FileNames: fileNames}, nil
}

// GetAudioFiles retrieves audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getaudiofiles
func GetAudioFiles(ctx context.Context, req *GetAudioFilesRequest) (*AudioFilesResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get audio files
	audioFiles, err := dbUtils.GetAudioFiles(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get audio files: %v", err),
		}
	}

	return &AudioFilesResponse{AudioFiles: audioFiles}, nil
}

// GetAudioFile retrieves a specific audio file
//
//encore:api public method=GET path=/api/track-surfer/getaudiofile
func GetAudioFile(ctx context.Context, req *GetAudioFileRequest) (*AudioFileResponse, error) {
	if req.RowKey == "" || req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing rowKey or partitionKey parameter",
		}
	}

	// Call database layer to get all audio files for the project, then find the specific one
	audioFiles, err := dbUtils.GetAudioFiles(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get audio files: %v", err),
		}
	}

	// Find the specific audio file by RowKey
	for _, audioFile := range audioFiles {
		if audioFile.RowKey == req.RowKey {
			return &AudioFileResponse{AudioFile: &audioFile}, nil
		}
	}

	return nil, &errs.Error{
		Code:    errs.NotFound,
		Message: "Audio file not found",
	}
}

// Alignment Items Endpoints

// GetAlignmentItems retrieves alignment items for audio file and category
//
//encore:api public method=GET path=/api/track-surfer/getalignmentitems
func GetAlignmentItems(ctx context.Context, req *GetAlignmentItemsRequest) (*AlignmentItemsResponse, error) {
	if req.AudioFileID == "" || req.Category == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Call database layer to get alignment items
	alignmentItems, err := dbUtils.GetAlignmentItems(req.AudioFileID, req.Category)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get alignment items: %v", err),
		}
	}

	return &AlignmentItemsResponse{AlignmentItems: alignmentItems}, nil
}

// GetCountOfProjectAlignmentItems gets count of alignment items for a project and category
//
//encore:api public method=GET path=/api/track-surfer/getcountofprojectalignmentitems
func GetCountOfProjectAlignmentItems(ctx context.Context, req *GetCountAlignmentItemsRequest) (*CountResponse, error) {
	if req.PartitionKey == "" || req.Category == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing projectFolder or category parameter",
		}
	}

	// Call database layer to get count
	count, err := dbUtils.GetCountOfProjectAlignmentItems(req.PartitionKey, req.Category)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get alignment item count: %v", err),
		}
	}

	return &CountResponse{Count: count}, nil
}

// GetConfirmedAlignmentItemCount gets count of confirmed alignment items
//
//encore:api public method=GET path=/api/track-surfer/getconfirmedalignmentitemcount
func GetConfirmedAlignmentItemCount(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get confirmed count
	count, err := dbUtils.GetCountOfConfirmedAlignmentItems(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get confirmed alignment item count: %v", err),
		}
	}

	return &CountResponse{Count: count}, nil
}

// GetAlignmentItemCount gets total alignment item count
//
//encore:api public method=GET path=/api/track-surfer/getalignmentitemcount
func GetAlignmentItemCount(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get total alignment item count
	count, err := dbUtils.GetAlignmentItemCount(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get alignment item count: %v", err),
		}
	}

	return &CountResponse{Count: count}, nil
}

// GetOmissionCount gets omission count
//
//encore:api public method=GET path=/api/track-surfer/getomissioncount
func GetOmissionCount(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get omission count
	count, err := dbUtils.GetOmissionCount(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get omission count: %v", err),
		}
	}

	return &CountResponse{Count: count}, nil
}

// GetInsertionCount gets insertion count
//
//encore:api public method=GET path=/api/track-surfer/getinsertioncount
func GetInsertionCount(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get insertion count
	count, err := dbUtils.GetInsertionCount(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get insertion count: %v", err),
		}
	}

	return &CountResponse{Count: count}, nil
}

// DeleteAlignmentItems deletes alignment items for a project
//
//encore:api public method=DELETE path=/api/track-surfer/deletealignmentitems
func DeleteAlignmentItems(ctx context.Context, req *GetCountRequest) (*DeleteResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to delete alignment items (including transcription)
	count, err := dbUtils.DeleteAllAlignmentItems(req.PartitionKey, true)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete alignment items: %v", err),
		}
	}

	return &DeleteResponse{Message: fmt.Sprintf("%d alignment items deleted", count)}, nil
}

// Status Check Endpoints

// IsTextExtracted checks if text is extracted for a project
//
//encore:api public method=GET path=/api/track-surfer/istextextracted
func IsTextExtracted(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call the database layer to check if text is extracted
	status, err := dbUtils.IsTextExtracted(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check text extraction status: %v", err),
		}
	}

	return &StatusResponse{Status: status}, nil
}

// IsTranscribed checks if project is transcribed
//
//encore:api public method=GET path=/api/track-surfer/istranscribed
func IsTranscribed(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if project is transcribed
	status, err := dbUtils.IsTranscribed(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check transcription status: %v", err),
		}
	}

	return &StatusResponse{Status: status}, nil
}

// IsAligned checks if project alignment is complete
//
//encore:api public method=GET path=/api/track-surfer/isaligned
func IsAligned(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if alignment is complete
	status, err := dbUtils.IsAlignmentComplete(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check alignment status: %v", err),
		}
	}

	return &StatusResponse{Status: status}, nil
}

// IsTranscoded checks if project is transcoded
//
//encore:api public method=GET path=/api/track-surfer/istranscoded
func IsTranscoded(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if project is transcoded
	status, err := dbUtils.IsTranscoded(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check transcoding status: %v", err),
		}
	}

	return &StatusResponse{Status: status}, nil
}

// IsAudioUploaded checks if audio is uploaded for a project
//
//encore:api public method=GET path=/api/track-surfer/isaudiouploaded
func IsAudioUploaded(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if audio is uploaded
	status, err := dbUtils.IsAudioUploaded(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check audio upload status: %v", err),
		}
	}

	return &StatusResponse{Status: status}, nil
}

// Transcription Endpoints

// GetTranscription retrieves transcription for an audio file
//
//encore:api public method=GET path=/api/track-surfer/gettranscription
func GetTranscription(ctx context.Context, req *GetTranscriptionRequest) (*TranscriptionResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Call database layer to get transcription
	transcriptionItems, err := dbUtils.GetTranscription(req.AudioFileID)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get transcription: %v", err),
		}
	}

	// Convert alignment items to transcription text
	var transcriptionText string
	var wordCount int
	var totalConfidence float64

	for _, item := range transcriptionItems {
		if item.Category == "Word" {
			transcriptionText += item.Text + " "
			wordCount++
			totalConfidence += item.Confidence
		}
	}

	// Calculate average confidence
	var avgConfidence float64
	if wordCount > 0 {
		avgConfidence = totalConfidence / float64(wordCount)
	}

	return &TranscriptionResponse{
		Transcription: &Transcription{
			AudioFileID: req.AudioFileID,
			Text:        transcriptionText,
			Language:    "English", // Default language
			Confidence:  avgConfidence,
			WordCount:   wordCount,
		},
	}, nil
}

// DeleteTranscription deletes transcription for a project
//
//encore:api public method=DELETE path=/api/track-surfer/deletetranscription
func DeleteTranscription(ctx context.Context, req *GetCountRequest) (*DeleteResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to delete transcription
	count, err := dbUtils.DeleteTranscription(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete transcription: %v", err),
		}
	}

	return &DeleteResponse{Message: fmt.Sprintf("%d words deleted", count)}, nil
}
