// Package get_api provides API endpoints for TrackSurfer application
package get_api

import (
	"context"
	"fmt"
	"encore.dev/beta/errs"
	"encore.app/infrastructure"
	"encore.app/types"
)

// =============================================================================
// USER MANAGEMENT ENDPOINTS
// =============================================================================

// Get<PERSON>ser retrieves a specific user by partition key
//
//encore:api public method=GET path=/api/track-surfer/getuser
func GetUser(ctx context.Context, req *types.GetUserRequest) (*types.UserResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing user's partitionKey parameter",
		}
	}

	// Call database layer to get user
	user, err := infrastructure.DbUtils.GetUser(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve user: %v", err),
		}
	}

	return &types.UserResponse{User: user}, nil
}

// GetUsers retrieves all users
//
//encore:api public method=GET path=/api/track-surfer/getusers
func GetUsers(ctx context.Context) (*types.UsersResponse, error) {
	// Call database layer to get all users
	users, err := infrastructure.DbUtils.GetAllUsers()
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve users: %v", err),
		}
	}

	return &types.UsersResponse{Users: users}, nil
}

// =============================================================================
// PROJECT MANAGEMENT ENDPOINTS
// =============================================================================

// GetAllProjects retrieves all projects
//
//encore:api public method=GET path=/api/track-surfer/getallprojects
func GetAllProjects(ctx context.Context) (*types.ProjectsResponse, error) {
	// Call database layer to get all projects
	projects, err := infrastructure.DbUtils.GetAllProjects()
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve projects: %v", err),
		}
	}

	return &types.ProjectsResponse{Projects: projects}, nil
}

// GetProject retrieves a specific project by row key
//
//encore:api public method=GET path=/api/track-surfer/getproject
func GetProject(ctx context.Context, req *types.GetProjectRequest) (*types.ProjectResponse, error) {
	if req.RowKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project's rowKey parameter",
		}
	}

	// Call database layer to get project
	project, err := infrastructure.DbUtils.GetProject(req.RowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve project: %v", err),
		}
	}

	return &types.ProjectResponse{Project: project}, nil
}

// GetBook retrieves book information by row key
//
//encore:api public method=GET path=/api/track-surfer/getbook
func GetBook(ctx context.Context, req *types.GetBookRequest) (*types.BookResponse, error) {
	if req.RowKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project's rowKey parameter",
		}
	}

	// Call database layer to get book
	book, err := infrastructure.DbUtils.GetBook(req.RowKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to retrieve book: %v", err),
		}
	}

	return &types.BookResponse{Book: book}, nil
}

// =============================================================================
// AUDIO FILE MANAGEMENT ENDPOINTS
// =============================================================================

// GetCountOfProjectAudioFiles gets the count of audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getcountofprojectaudiofiles
func GetCountOfProjectAudioFiles(ctx context.Context, req *types.GetCountRequest) (*types.CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get count
	count, err := infrastructure.DbUtils.GetCountOfProjectAudioFiles(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get audio file count: %v", err),
		}
	}

	return &types.CountResponse{Count: count}, nil
}

// GetListOfProjectAudioFiles gets the list of audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getlistofprojectaudiofiles
func GetListOfProjectAudioFiles(ctx context.Context, req *types.GetListRequest) (*types.AudioFileListResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get file list
	fileNames, err := infrastructure.DbUtils.GetListOfProjectAudioFiles(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get audio file list: %v", err),
		}
	}

	return &types.AudioFileListResponse{FileNames: fileNames}, nil
}

// GetAudioFiles retrieves audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getaudiofiles
func GetAudioFiles(ctx context.Context, req *types.GetAudioFilesRequest) (*types.AudioFilesResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get audio files
	audioFiles, err := infrastructure.DbUtils.GetAudioFiles(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get audio files: %v", err),
		}
	}

	return &types.AudioFilesResponse{AudioFiles: audioFiles}, nil
}

// GetAudioFile retrieves a specific audio file
//
//encore:api public method=GET path=/api/track-surfer/getaudiofile
func GetAudioFile(ctx context.Context, req *types.GetAudioFileRequest) (*types.AudioFileResponse, error) {
	if req.RowKey == "" || req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing rowKey or partitionKey parameter",
		}
	}

	// Call database layer to get all audio files for the project, then find the specific one
	audioFiles, err := infrastructure.DbUtils.GetAudioFiles(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get audio files: %v", err),
		}
	}

	// Find the specific audio file by RowKey
	for _, audioFile := range audioFiles {
		if audioFile.RowKey == req.RowKey {
			return &types.AudioFileResponse{AudioFile: &audioFile}, nil
		}
	}

	return nil, &errs.Error{
		Code:    errs.NotFound,
		Message: "Audio file not found",
	}
}

// =============================================================================
// ALIGNMENT ITEMS ENDPOINTS
// =============================================================================

// GetAlignmentItems retrieves alignment items for audio file and category
//
//encore:api public method=GET path=/api/track-surfer/getalignmentitems
func GetAlignmentItems(ctx context.Context, req *types.GetAlignmentItemsRequest) (*types.AlignmentItemsResponse, error) {
	if req.AudioFileID == "" || req.Category == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Call database layer to get alignment items
	alignmentItems, err := infrastructure.DbUtils.GetAlignmentItems(req.AudioFileID, req.Category)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get alignment items: %v", err),
		}
	}

	return &types.AlignmentItemsResponse{AlignmentItems: alignmentItems}, nil
}

// GetCountOfProjectAlignmentItems gets count of alignment items for a project and category
//
//encore:api public method=GET path=/api/track-surfer/getcountofprojectalignmentitems
func GetCountOfProjectAlignmentItems(ctx context.Context, req *types.GetCountAlignmentItemsRequest) (*types.CountResponse, error) {
	if req.PartitionKey == "" || req.Category == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing projectFolder or category parameter",
		}
	}

	// Call database layer to get count
	count, err := infrastructure.DbUtils.GetCountOfProjectAlignmentItems(req.PartitionKey, req.Category)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get alignment item count: %v", err),
		}
	}

	return &types.CountResponse{Count: count}, nil
}

// GetConfirmedAlignmentItemCount gets count of confirmed alignment items
//
//encore:api public method=GET path=/api/track-surfer/getconfirmedalignmentitemcount
func GetConfirmedAlignmentItemCount(ctx context.Context, req *types.GetCountRequest) (*types.CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get confirmed count
	count, err := infrastructure.DbUtils.GetCountOfConfirmedAlignmentItems(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get confirmed alignment item count: %v", err),
		}
	}

	return &types.CountResponse{Count: count}, nil
}

// GetAlignmentItemCount gets total alignment item count
//
//encore:api public method=GET path=/api/track-surfer/getalignmentitemcount
func GetAlignmentItemCount(ctx context.Context, req *types.GetCountRequest) (*types.CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get total alignment item count
	count, err := infrastructure.DbUtils.GetAlignmentItemCount(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get alignment item count: %v", err),
		}
	}

	return &types.CountResponse{Count: count}, nil
}

// GetOmissionCount gets omission count
//
//encore:api public method=GET path=/api/track-surfer/getomissioncount
func GetOmissionCount(ctx context.Context, req *types.GetCountRequest) (*types.CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get omission count
	count, err := infrastructure.DbUtils.GetOmissionCount(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get omission count: %v", err),
		}
	}

	return &types.CountResponse{Count: count}, nil
}

// GetInsertionCount gets insertion count
//
//encore:api public method=GET path=/api/track-surfer/getinsertioncount
func GetInsertionCount(ctx context.Context, req *types.GetCountRequest) (*types.CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to get insertion count
	count, err := infrastructure.DbUtils.GetInsertionCount(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get insertion count: %v", err),
		}
	}

	return &types.CountResponse{Count: count}, nil
}

// =============================================================================
// DELETE OPERATIONS
// =============================================================================

// DeleteAlignmentItems deletes alignment items for a project
//
//encore:api public method=DELETE path=/api/track-surfer/deletealignmentitems
func DeleteAlignmentItems(ctx context.Context, req *types.GetCountRequest) (*types.DeleteResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to delete alignment items (including transcription)
	count, err := infrastructure.DbUtils.DeleteAllAlignmentItems(req.PartitionKey, true)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete alignment items: %v", err),
		}
	}

	return &types.DeleteResponse{Message: fmt.Sprintf("%d alignment items deleted", count)}, nil
}

// =============================================================================
// STATUS CHECK ENDPOINTS
// =============================================================================

// IsTextExtracted checks if text is extracted for a project
//
//encore:api public method=GET path=/api/track-surfer/istextextracted
func IsTextExtracted(ctx context.Context, req *types.GetCountRequest) (*types.StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if text is extracted
	status, err := infrastructure.DbUtils.IsTextExtracted(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check text extraction status: %v", err),
		}
	}

	return &types.StatusResponse{Status: status}, nil
}

// IsTranscribed checks if project is transcribed
//
//encore:api public method=GET path=/api/track-surfer/istranscribed
func IsTranscribed(ctx context.Context, req *types.GetCountRequest) (*types.StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if project is transcribed
	status, err := infrastructure.DbUtils.IsTranscribed(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check transcription status: %v", err),
		}
	}

	return &types.StatusResponse{Status: status}, nil
}

// IsAligned checks if project alignment is complete
//
//encore:api public method=GET path=/api/track-surfer/isaligned
func IsAligned(ctx context.Context, req *types.GetCountRequest) (*types.StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if alignment is complete
	status, err := infrastructure.DbUtils.IsAlignmentComplete(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check alignment status: %v", err),
		}
	}

	return &types.StatusResponse{Status: status}, nil
}

// IsTranscoded checks if project is transcoded
//
//encore:api public method=GET path=/api/track-surfer/istranscoded
func IsTranscoded(ctx context.Context, req *types.GetCountRequest) (*types.StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if project is transcoded
	status, err := infrastructure.DbUtils.IsTranscoded(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check transcoding status: %v", err),
		}
	}

	return &types.StatusResponse{Status: status}, nil
}

// IsAudioUploaded checks if audio is uploaded for a project
//
//encore:api public method=GET path=/api/track-surfer/isaudiouploaded
func IsAudioUploaded(ctx context.Context, req *types.GetCountRequest) (*types.StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to check if audio is uploaded
	status, err := infrastructure.DbUtils.IsAudioUploaded(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to check audio upload status: %v", err),
		}
	}

	return &types.StatusResponse{Status: status}, nil
}

// =============================================================================
// TRANSCRIPTION ENDPOINTS
// =============================================================================

// GetTranscription retrieves transcription for an audio file
//
//encore:api public method=GET path=/api/track-surfer/gettranscription
func GetTranscription(ctx context.Context, req *types.GetTranscriptionRequest) (*types.TranscriptionResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Call database layer to get transcription
	transcriptionItems, err := infrastructure.DbUtils.GetTranscription(req.AudioFileID)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to get transcription: %v", err),
		}
	}

	// Convert alignment items to transcription text
	var transcriptionText string
	var wordCount int
	var totalConfidence float64

	for _, item := range transcriptionItems {
		if item.Category == "Word" {
			transcriptionText += item.Text + " "
			wordCount++
			totalConfidence += item.Confidence
		}
	}

	// Calculate average confidence
	var avgConfidence float64
	if wordCount > 0 {
		avgConfidence = totalConfidence / float64(wordCount)
	}

	return &types.TranscriptionResponse{
		Transcription: &infrastructure.Transcription{
			AudioFileID: req.AudioFileID,
			Text:        transcriptionText,
			Language:    "English", // Default language
			Confidence:  avgConfidence,
			WordCount:   wordCount,
		},
	}, nil
}

// DeleteTranscription deletes transcription for a project
//
//encore:api public method=DELETE path=/api/track-surfer/deletetranscription
func DeleteTranscription(ctx context.Context, req *types.GetCountRequest) (*types.DeleteResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Call database layer to delete transcription
	count, err := infrastructure.DbUtils.DeleteTranscription(req.PartitionKey)
	if err != nil {
		return nil, &errs.Error{
			Code:    errs.Internal,
			Message: fmt.Sprintf("Failed to delete transcription: %v", err),
		}
	}

	return &types.DeleteResponse{Message: fmt.Sprintf("%d words deleted", count)}, nil
}