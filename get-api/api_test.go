package get_api

import (
	"context"
	"strings"
	"testing"
	"encore.app/types"
)

// Run tests using `encore test`, which compiles the Encore app and then runs `go test`.
// It supports all the same flags that the `go test` command does.
// You automatically get tracing for tests in the local dev dash: http://localhost:9400
// Learn more: https://encore.dev/docs/go/develop/testing

// =============================================================================
// USER MANAGEMENT TESTS
// =============================================================================

func TestGetUser(t *testing.T) {
	req := &types.GetUserRequest{
		PartitionKey: "test_user_partition",
	}
	resp, err := GetUser(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.User == nil {
		t.Error("Expected user data, got nil")
	}
	if resp.User.PartitionKey != req.PartitionKey {
		t.Errorf("Expected partition key %q, got %q", req.PartitionKey, resp.User.PartitionKey)
	}
	if resp.User.RowKey == "" {
		t.Error("Expected user RowKey to be set")
	}
	if resp.User.Name == "" {
		t.Error("Expected user Name to be set")
	}
	if resp.User.Email == "" {
		t.Error("Expected user Email to be set")
	}
}

func TestGetUserMissingPartitionKey(t *testing.T) {
	req := &types.GetUserRequest{
		PartitionKey: "",
	}
	_, err := GetUser(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetUsers(t *testing.T) {
	resp, err := GetUsers(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	if len(resp.Users) == 0 {
		t.Error("Expected users data, got empty slice")
	}

	// Verify each user has required fields
	for i, user := range resp.Users {
		if user.PartitionKey == "" {
			t.Errorf("User %d: Expected PartitionKey to be set", i)
		}
		if user.RowKey == "" {
			t.Errorf("User %d: Expected RowKey to be set", i)
		}
		if user.Name == "" {
			t.Errorf("User %d: Expected Name to be set", i)
		}
		if user.Email == "" {
			t.Errorf("User %d: Expected Email to be set", i)
		}
	}
}

// =============================================================================
// PROJECT MANAGEMENT TESTS
// =============================================================================

func TestGetAllProjects(t *testing.T) {
	resp, err := GetAllProjects(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	if len(resp.Projects) == 0 {
		t.Error("Expected projects data, got empty slice")
	}

	// Verify each project has required fields
	for i, project := range resp.Projects {
		if project.PartitionKey == "" {
			t.Errorf("Project %d: Expected PartitionKey to be set", i)
		}
		if project.RowKey == "" {
			t.Errorf("Project %d: Expected RowKey to be set", i)
		}
		if project.Name == "" {
			t.Errorf("Project %d: Expected Name to be set", i)
		}
		if project.Status == "" {
			t.Errorf("Project %d: Expected Status to be set", i)
		}
	}
}

func TestGetProject(t *testing.T) {
	req := &types.GetProjectRequest{
		RowKey: "test_project_001",
	}
	resp, err := GetProject(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Project == nil {
		t.Error("Expected project data, got nil")
	}
	if resp.Project.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.Project.RowKey)
	}
	if resp.Project.Name == "" {
		t.Error("Expected project Name to be set")
	}
	if resp.Project.Status == "" {
		t.Error("Expected project Status to be set")
	}
}

func TestGetProjectMissingRowKey(t *testing.T) {
	req := &types.GetProjectRequest{
		RowKey: "",
	}
	_, err := GetProject(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing row key, got nil")
	}
}

func TestGetBook(t *testing.T) {
	req := &types.GetBookRequest{
		RowKey: "test_project_001",
	}
	resp, err := GetBook(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Book == nil {
		t.Error("Expected book data, got nil")
	}
	if resp.Book.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.Book.RowKey)
	}
	if resp.Book.Title == "" {
		t.Error("Expected book Title to be set")
	}
	if resp.Book.Author == "" {
		t.Error("Expected book Author to be set")
	}
}

func TestGetBookMissingRowKey(t *testing.T) {
	req := &types.GetBookRequest{
		RowKey: "",
	}
	_, err := GetBook(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing row key, got nil")
	}
}

// =============================================================================
// AUDIO FILE MANAGEMENT TESTS
// =============================================================================

func TestGetCountOfProjectAudioFiles(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetCountOfProjectAudioFiles(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Count < 0 {
		t.Error("Expected non-negative count, got negative")
	}
}

func TestGetCountOfProjectAudioFilesMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := GetCountOfProjectAudioFiles(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetListOfProjectAudioFiles(t *testing.T) {
	req := &types.GetListRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetListOfProjectAudioFiles(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.FileNames == nil {
		t.Error("Expected file names slice, got nil")
	}

	// Verify file names are valid
	for i, fileName := range resp.FileNames {
		if fileName == "" {
			t.Errorf("File %d: Expected non-empty file name", i)
		}
		if !strings.HasSuffix(fileName, ".mp3") && !strings.HasSuffix(fileName, ".wav") {
			t.Errorf("File %d: Expected audio file extension, got %q", i, fileName)
		}
	}
}

func TestGetListOfProjectAudioFilesMissingPartitionKey(t *testing.T) {
	req := &types.GetListRequest{
		PartitionKey: "",
	}
	_, err := GetListOfProjectAudioFiles(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetAudioFiles(t *testing.T) {
	req := &types.GetAudioFilesRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetAudioFiles(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.AudioFiles == nil {
		t.Error("Expected audio files slice, got nil")
	}

	// Verify each audio file has required fields
	for i, audioFile := range resp.AudioFiles {
		if audioFile.PartitionKey == "" {
			t.Errorf("AudioFile %d: Expected PartitionKey to be set", i)
		}
		if audioFile.RowKey == "" {
			t.Errorf("AudioFile %d: Expected RowKey to be set", i)
		}
		if audioFile.FileName == "" {
			t.Errorf("AudioFile %d: Expected FileName to be set", i)
		}
		if audioFile.Duration <= 0 {
			t.Errorf("AudioFile %d: Expected positive Duration, got %f", i, audioFile.Duration)
		}
		if audioFile.FileSize <= 0 {
			t.Errorf("AudioFile %d: Expected positive FileSize, got %d", i, audioFile.FileSize)
		}
	}
}

func TestGetAudioFilesMissingPartitionKey(t *testing.T) {
	req := &types.GetAudioFilesRequest{
		PartitionKey: "",
	}
	_, err := GetAudioFiles(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetAudioFile(t *testing.T) {
	req := &types.GetAudioFileRequest{
		PartitionKey: "test_partition",
		RowKey:       "audio_001",
	}
	resp, err := GetAudioFile(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.AudioFile == nil {
		t.Error("Expected audio file data, got nil")
	}
	if resp.AudioFile.PartitionKey != req.PartitionKey {
		t.Errorf("Expected partition key %q, got %q", req.PartitionKey, resp.AudioFile.PartitionKey)
	}
	if resp.AudioFile.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.AudioFile.RowKey)
	}
}

func TestGetAudioFileMissingParameters(t *testing.T) {
	tests := []struct {
		name         string
		partitionKey string
		rowKey       string
	}{
		{"Missing PartitionKey", "", "audio_001"},
		{"Missing RowKey", "test_partition", ""},
		{"Missing Both", "", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &types.GetAudioFileRequest{
				PartitionKey: tt.partitionKey,
				RowKey:       tt.rowKey,
			}
			_, err := GetAudioFile(context.Background(), req)
			if err == nil {
				t.Error("Expected error for missing parameters, got nil")
			}
		})
	}
}

func TestGetAudioFileNotFound(t *testing.T) {
	req := &types.GetAudioFileRequest{
		PartitionKey: "test_partition",
		RowKey:       "nonexistent_audio",
	}
	_, err := GetAudioFile(context.Background(), req)
	if err == nil {
		t.Error("Expected error for non-existent audio file, got nil")
	}
}

// =============================================================================
// ALIGNMENT ITEMS TESTS
// =============================================================================

func TestGetAlignmentItems(t *testing.T) {
	req := &types.GetAlignmentItemsRequest{
		AudioFileID: "test_audio_001",
		Category:    "All",
	}
	resp, err := GetAlignmentItems(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.AlignmentItems == nil {
		t.Error("Expected alignment items slice, got nil")
	}

	// Verify each alignment item has required fields
	for i, item := range resp.AlignmentItems {
		if item.ID == "" {
			t.Errorf("AlignmentItem %d: Expected ID to be set", i)
		}
		if item.AudioFileID == "" {
			t.Errorf("AlignmentItem %d: Expected AudioFileID to be set", i)
		}
		if item.Category == "" {
			t.Errorf("AlignmentItem %d: Expected Category to be set", i)
		}
		if item.StartTime < 0 {
			t.Errorf("AlignmentItem %d: Expected non-negative StartTime, got %f", i, item.StartTime)
		}
		if item.EndTime <= item.StartTime {
			t.Errorf("AlignmentItem %d: Expected EndTime > StartTime, got %f <= %f", i, item.EndTime, item.StartTime)
		}
		if item.Confidence < 0 || item.Confidence > 1 {
			t.Errorf("AlignmentItem %d: Expected Confidence between 0 and 1, got %f", i, item.Confidence)
		}
	}
}

func TestGetAlignmentItemsMissingParameters(t *testing.T) {
	tests := []struct {
		name        string
		audioFileID string
		category    string
	}{
		{"Missing AudioFileID", "", "All"},
		{"Missing Category", "test_audio_001", ""},
		{"Missing Both", "", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &types.GetAlignmentItemsRequest{
				AudioFileID: tt.audioFileID,
				Category:    tt.category,
			}
			_, err := GetAlignmentItems(context.Background(), req)
			if err == nil {
				t.Error("Expected error for missing parameters, got nil")
			}
		})
	}
}

func TestGetAlignmentItemsWithDifferentCategories(t *testing.T) {
	categories := []string{"All", "Word", "Alignment", "Omission", "Insertion"}

	for _, category := range categories {
		t.Run("Category_"+category, func(t *testing.T) {
			req := &types.GetAlignmentItemsRequest{
				AudioFileID: "test_audio_001",
				Category:    category,
			}
			resp, err := GetAlignmentItems(context.Background(), req)
			if err != nil {
				t.Fatal(err)
			}
			if resp.AlignmentItems == nil {
				t.Error("Expected alignment items slice, got nil")
			}
		})
	}
}

func TestGetCountOfProjectAlignmentItems(t *testing.T) {
	req := &types.GetCountAlignmentItemsRequest{
		PartitionKey: "test_partition",
		Category:     "All",
	}
	resp, err := GetCountOfProjectAlignmentItems(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Count < 0 {
		t.Error("Expected non-negative count, got negative")
	}
}

func TestGetCountOfProjectAlignmentItemsMissingParameters(t *testing.T) {
	tests := []struct {
		name         string
		partitionKey string
		category     string
	}{
		{"Missing PartitionKey", "", "All"},
		{"Missing Category", "test_partition", ""},
		{"Missing Both", "", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &types.GetCountAlignmentItemsRequest{
				PartitionKey: tt.partitionKey,
				Category:     tt.category,
			}
			_, err := GetCountOfProjectAlignmentItems(context.Background(), req)
			if err == nil {
				t.Error("Expected error for missing parameters, got nil")
			}
		})
	}
}

func TestGetConfirmedAlignmentItemCount(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetConfirmedAlignmentItemCount(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Count < 0 {
		t.Error("Expected non-negative count, got negative")
	}
}

func TestGetConfirmedAlignmentItemCountMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := GetConfirmedAlignmentItemCount(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetAlignmentItemCount(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetAlignmentItemCount(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Count < 0 {
		t.Error("Expected non-negative count, got negative")
	}
}

func TestGetOmissionCount(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetOmissionCount(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Count < 0 {
		t.Error("Expected non-negative count, got negative")
	}
}

func TestGetInsertionCount(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetInsertionCount(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Count < 0 {
		t.Error("Expected non-negative count, got negative")
	}
}

// =============================================================================
// DELETE OPERATIONS TESTS
// =============================================================================

func TestDeleteAlignmentItems(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := DeleteAlignmentItems(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected deletion message, got empty string")
	}
	if !strings.Contains(resp.Message, "alignment items deleted") {
		t.Errorf("Expected deletion message to contain 'alignment items deleted', got %q", resp.Message)
	}
}

func TestDeleteAlignmentItemsMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := DeleteAlignmentItems(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

// =============================================================================
// STATUS CHECK TESTS
// =============================================================================

func TestIsTextExtracted(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := IsTextExtracted(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	// Status can be true or false, just verify it's a boolean response
	_ = resp.Status
}

func TestIsTextExtractedMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := IsTextExtracted(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestIsTranscribed(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := IsTranscribed(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	_ = resp.Status
}

func TestIsTranscribedMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := IsTranscribed(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestIsAligned(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := IsAligned(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	_ = resp.Status
}

func TestIsAlignedMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := IsAligned(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestIsTranscoded(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := IsTranscoded(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	_ = resp.Status
}

func TestIsTranscodedMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := IsTranscoded(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestIsAudioUploaded(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := IsAudioUploaded(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	_ = resp.Status
}

func TestIsAudioUploadedMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := IsAudioUploaded(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

// =============================================================================
// TRANSCRIPTION TESTS
// =============================================================================

func TestGetTranscription(t *testing.T) {
	req := &types.GetTranscriptionRequest{
		AudioFileID: "test_audio_001",
	}
	resp, err := GetTranscription(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Transcription == nil {
		t.Error("Expected transcription data, got nil")
	}
	if resp.Transcription.AudioFileID != req.AudioFileID {
		t.Errorf("Expected audio file ID %q, got %q", req.AudioFileID, resp.Transcription.AudioFileID)
	}
	if resp.Transcription.WordCount < 0 {
		t.Error("Expected non-negative word count")
	}
	if resp.Transcription.Confidence < 0 || resp.Transcription.Confidence > 1 {
		t.Errorf("Expected confidence between 0 and 1, got %f", resp.Transcription.Confidence)
	}
}

func TestGetTranscriptionMissingAudioFileID(t *testing.T) {
	req := &types.GetTranscriptionRequest{
		AudioFileID: "",
	}
	_, err := GetTranscription(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing audio file ID, got nil")
	}
}

func TestDeleteTranscription(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := DeleteTranscription(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected deletion message, got empty string")
	}
	if !strings.Contains(resp.Message, "words deleted") {
		t.Errorf("Expected deletion message to contain 'words deleted', got %q", resp.Message)
	}
}

func TestDeleteTranscriptionMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := DeleteTranscription(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

// =============================================================================
// INTEGRATION TESTS
// =============================================================================

func TestEndToEndWorkflow(t *testing.T) {
	// Test a complete workflow: Project -> Audio Files -> Alignment Items -> Transcription

	// 1. Get project
	projectReq := &types.GetProjectRequest{RowKey: "test_project_001"}
	projectResp, err := GetProject(context.Background(), projectReq)
	if err != nil {
		t.Fatal("Failed to get project:", err)
	}

	// 2. Get audio files for the project
	audioFilesReq := &types.GetAudioFilesRequest{PartitionKey: projectResp.Project.PartitionKey}
	audioFilesResp, err := GetAudioFiles(context.Background(), audioFilesReq)
	if err != nil {
		t.Fatal("Failed to get audio files:", err)
	}

	if len(audioFilesResp.AudioFiles) == 0 {
		t.Skip("No audio files found, skipping workflow test")
	}

	// 3. Get alignment items for the first audio file
	alignmentReq := &types.GetAlignmentItemsRequest{
		AudioFileID: audioFilesResp.AudioFiles[0].RowKey,
		Category:    "All",
	}
	alignmentResp, err := GetAlignmentItems(context.Background(), alignmentReq)
	if err != nil {
		t.Fatal("Failed to get alignment items:", err)
	}

	// 4. Get transcription for the audio file
	transcriptionReq := &types.GetTranscriptionRequest{
		AudioFileID: audioFilesResp.AudioFiles[0].RowKey,
	}
	transcriptionResp, err := GetTranscription(context.Background(), transcriptionReq)
	if err != nil {
		t.Fatal("Failed to get transcription:", err)
	}

	// Verify the workflow completed successfully
	t.Logf("Workflow completed: Project %s -> %d audio files -> %d alignment items -> %d words transcribed",
		projectResp.Project.Name,
		len(audioFilesResp.AudioFiles),
		len(alignmentResp.AlignmentItems),
		transcriptionResp.Transcription.WordCount)
}

func TestAllCountEndpointsReturnNonNegative(t *testing.T) {
	req := &types.GetCountRequest{PartitionKey: "test_partition"}

	tests := []struct {
		name string
		fn   func(context.Context, *types.GetCountRequest) (*types.CountResponse, error)
	}{
		{"GetCountOfProjectAudioFiles", GetCountOfProjectAudioFiles},
		{"GetConfirmedAlignmentItemCount", GetConfirmedAlignmentItemCount},
		{"GetAlignmentItemCount", GetAlignmentItemCount},
		{"GetOmissionCount", GetOmissionCount},
		{"GetInsertionCount", GetInsertionCount},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := tt.fn(context.Background(), req)
			if err != nil {
				t.Fatal(err)
			}
			if resp.Count < 0 {
				t.Errorf("Expected non-negative count, got %d", resp.Count)
			}
		})
	}
}

func TestAllStatusEndpointsReturnBoolean(t *testing.T) {
	req := &types.GetCountRequest{PartitionKey: "test_partition"}

	tests := []struct {
		name string
		fn   func(context.Context, *types.GetCountRequest) (*types.StatusResponse, error)
	}{
		{"IsTextExtracted", IsTextExtracted},
		{"IsTranscribed", IsTranscribed},
		{"IsAligned", IsAligned},
		{"IsTranscoded", IsTranscoded},
		{"IsAudioUploaded", IsAudioUploaded},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := tt.fn(context.Background(), req)
			if err != nil {
				t.Fatal(err)
			}
			// Just verify we get a response - status can be true or false
			_ = resp.Status
		})
	}
}
