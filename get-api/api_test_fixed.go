package get_api

import (
	"context"
	"strings"
	"testing"
	"encore.app/types"
)

// Run tests using `encore test`, which compiles the Encore app and then runs `go test`.
// It supports all the same flags that the `go test` command does.
// You automatically get tracing for tests in the local dev dash: http://localhost:9400
// Learn more: https://encore.dev/docs/go/develop/testing

// =============================================================================
// USER MANAGEMENT TESTS
// =============================================================================

func TestGetUser(t *testing.T) {
	req := &types.GetUserRequest{
		PartitionKey: "test_user_partition",
	}
	resp, err := GetUser(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.User == nil {
		t.Error("Expected user data, got nil")
	}
	if resp.User.PartitionKey != req.PartitionKey {
		t.Errorf("Expected partition key %q, got %q", req.PartitionKey, resp.User.PartitionKey)
	}
	if resp.User.RowKey == "" {
		t.Error("Expected user RowKey to be set")
	}
	if resp.User.Name == "" {
		t.Error("Expected user Name to be set")
	}
	if resp.User.Email == "" {
		t.Error("Expected user Email to be set")
	}
}

func TestGetUserMissingPartitionKey(t *testing.T) {
	req := &types.GetUserRequest{
		PartitionKey: "",
	}
	_, err := GetUser(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetUsers(t *testing.T) {
	resp, err := GetUsers(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	if len(resp.Users) == 0 {
		t.Error("Expected users data, got empty slice")
	}

	// Verify each user has required fields
	for i, user := range resp.Users {
		if user.PartitionKey == "" {
			t.Errorf("User %d: Expected PartitionKey to be set", i)
		}
		if user.RowKey == "" {
			t.Errorf("User %d: Expected RowKey to be set", i)
		}
		if user.Name == "" {
			t.Errorf("User %d: Expected Name to be set", i)
		}
		if user.Email == "" {
			t.Errorf("User %d: Expected Email to be set", i)
		}
	}
}

// =============================================================================
// PROJECT MANAGEMENT TESTS
// =============================================================================

func TestGetAllProjects(t *testing.T) {
	resp, err := GetAllProjects(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	if len(resp.Projects) == 0 {
		t.Error("Expected projects data, got empty slice")
	}

	// Verify each project has required fields
	for i, project := range resp.Projects {
		if project.PartitionKey == "" {
			t.Errorf("Project %d: Expected PartitionKey to be set", i)
		}
		if project.RowKey == "" {
			t.Errorf("Project %d: Expected RowKey to be set", i)
		}
		if project.Name == "" {
			t.Errorf("Project %d: Expected Name to be set", i)
		}
		if project.Status == "" {
			t.Errorf("Project %d: Expected Status to be set", i)
		}
	}
}

func TestGetProject(t *testing.T) {
	req := &types.GetProjectRequest{
		RowKey: "test_project_001",
	}
	resp, err := GetProject(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Project == nil {
		t.Error("Expected project data, got nil")
	}
	if resp.Project.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.Project.RowKey)
	}
	if resp.Project.Name == "" {
		t.Error("Expected project Name to be set")
	}
	if resp.Project.Status == "" {
		t.Error("Expected project Status to be set")
	}
}

func TestGetProjectMissingRowKey(t *testing.T) {
	req := &types.GetProjectRequest{
		RowKey: "",
	}
	_, err := GetProject(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing row key, got nil")
	}
}

func TestGetBook(t *testing.T) {
	req := &types.GetBookRequest{
		RowKey: "test_project_001",
	}
	resp, err := GetBook(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Book == nil {
		t.Error("Expected book data, got nil")
	}
	if resp.Book.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.Book.RowKey)
	}
	if resp.Book.Title == "" {
		t.Error("Expected book Title to be set")
	}
	if resp.Book.Author == "" {
		t.Error("Expected book Author to be set")
	}
}

func TestGetBookMissingRowKey(t *testing.T) {
	req := &types.GetBookRequest{
		RowKey: "",
	}
	_, err := GetBook(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing row key, got nil")
	}
}

// =============================================================================
// AUDIO FILE MANAGEMENT TESTS
// =============================================================================

func TestGetCountOfProjectAudioFiles(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetCountOfProjectAudioFiles(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Count < 0 {
		t.Error("Expected non-negative count, got negative")
	}
}

func TestGetCountOfProjectAudioFilesMissingPartitionKey(t *testing.T) {
	req := &types.GetCountRequest{
		PartitionKey: "",
	}
	_, err := GetCountOfProjectAudioFiles(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetListOfProjectAudioFiles(t *testing.T) {
	req := &types.GetListRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetListOfProjectAudioFiles(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.FileNames == nil {
		t.Error("Expected file names slice, got nil")
	}

	// Verify file names are valid
	for i, fileName := range resp.FileNames {
		if fileName == "" {
			t.Errorf("File %d: Expected non-empty file name", i)
		}
		if !strings.HasSuffix(fileName, ".mp3") && !strings.HasSuffix(fileName, ".wav") {
			t.Errorf("File %d: Expected audio file extension, got %q", i, fileName)
		}
	}
}

func TestGetListOfProjectAudioFilesMissingPartitionKey(t *testing.T) {
	req := &types.GetListRequest{
		PartitionKey: "",
	}
	_, err := GetListOfProjectAudioFiles(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetAudioFiles(t *testing.T) {
	req := &types.GetAudioFilesRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetAudioFiles(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.AudioFiles == nil {
		t.Error("Expected audio files slice, got nil")
	}

	// Verify each audio file has required fields
	for i, audioFile := range resp.AudioFiles {
		if audioFile.PartitionKey == "" {
			t.Errorf("AudioFile %d: Expected PartitionKey to be set", i)
		}
		if audioFile.RowKey == "" {
			t.Errorf("AudioFile %d: Expected RowKey to be set", i)
		}
		if audioFile.FileName == "" {
			t.Errorf("AudioFile %d: Expected FileName to be set", i)
		}
		if audioFile.Duration <= 0 {
			t.Errorf("AudioFile %d: Expected positive Duration, got %f", i, audioFile.Duration)
		}
		if audioFile.FileSize <= 0 {
			t.Errorf("AudioFile %d: Expected positive FileSize, got %d", i, audioFile.FileSize)
		}
	}
}

func TestGetAudioFilesMissingPartitionKey(t *testing.T) {
	req := &types.GetAudioFilesRequest{
		PartitionKey: "",
	}
	_, err := GetAudioFiles(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetAudioFile(t *testing.T) {
	req := &types.GetAudioFileRequest{
		PartitionKey: "test_partition",
		RowKey:       "audio_001",
	}
	resp, err := GetAudioFile(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.AudioFile == nil {
		t.Error("Expected audio file data, got nil")
	}
	if resp.AudioFile.PartitionKey != req.PartitionKey {
		t.Errorf("Expected partition key %q, got %q", req.PartitionKey, resp.AudioFile.PartitionKey)
	}
	if resp.AudioFile.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.AudioFile.RowKey)
	}
}

func TestGetAudioFileMissingParameters(t *testing.T) {
	tests := []struct {
		name         string
		partitionKey string
		rowKey       string
	}{
		{"Missing PartitionKey", "", "audio_001"},
		{"Missing RowKey", "test_partition", ""},
		{"Missing Both", "", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &types.GetAudioFileRequest{
				PartitionKey: tt.partitionKey,
				RowKey:       tt.rowKey,
			}
			_, err := GetAudioFile(context.Background(), req)
			if err == nil {
				t.Error("Expected error for missing parameters, got nil")
			}
		})
	}
}

func TestGetAudioFileNotFound(t *testing.T) {
	req := &types.GetAudioFileRequest{
		PartitionKey: "test_partition",
		RowKey:       "nonexistent_audio",
	}
	_, err := GetAudioFile(context.Background(), req)
	if err == nil {
		t.Error("Expected error for non-existent audio file, got nil")
	}
}
