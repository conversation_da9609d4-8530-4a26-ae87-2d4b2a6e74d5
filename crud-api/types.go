package crud_api

import "time"

// Request Types

// GetUserRequest represents a request to get a specific user
type GetUserRequest struct {
	PartitionKey string `query:"partitionKey"`
}

// GetProjectRequest represents a request to get a specific project
type GetProjectRequest struct {
	Row<PERSON>ey string `query:"rowKey"`
}

// GetBookRequest represents a request to get book information
type GetBookRequest struct {
	RowKey string `query:"rowKey"`
}

// GetCountRequest represents a request that requires a partition key for counting
type GetCountRequest struct {
	PartitionKey string `query:"partitionKey"`
}

// GetListRequest represents a request to get a list of items
type GetListRequest struct {
	PartitionKey string `query:"partitionKey"`
}

// GetAudioFilesRequest represents a request to get audio files
type GetAudioFilesRequest struct {
	PartitionKey string `query:"partitionKey"`
}

// GetAudioFileRequest represents a request to get a specific audio file
type GetAudioFileRequest struct {
	RowKey       string `query:"rowKey"`
	PartitionKey string `query:"partitionKey"`
}

// GetAlignmentItemsRequest represents a request to get alignment items
type GetAlignmentItemsRequest struct {
	AudioFileID string `query:"audioFileID"`
	Category    string `query:"category"`
}

// GetCountAlignmentItemsRequest represents a request to count alignment items
type GetCountAlignmentItemsRequest struct {
	PartitionKey string `query:"partitionKey"`
	Category     string `query:"category"`
}

// GetTranscriptionRequest represents a request to get transcription
type GetTranscriptionRequest struct {
	AudioFileID string `query:"audioFileID"`
}

// Response Types

// User represents a user in the system
type User struct {
	PartitionKey string    `json:"partitionKey"`
	RowKey       string    `json:"rowKey"`
	Name         string    `json:"name"`
	Email        string    `json:"email"`
	CreatedDate  time.Time `json:"createdDate"`
	IsActive     bool      `json:"isActive"`
}

// UserResponse represents a single user response
type UserResponse struct {
	User *User `json:"user"`
}

// UsersResponse represents multiple users response
type UsersResponse struct {
	Users []User `json:"users"`
}

// Project represents a project in the system
type Project struct {
	PartitionKey    string    `json:"partitionKey"`
	RowKey          string    `json:"rowKey"`
	Name            string    `json:"name"`
	Description     string    `json:"description"`
	CreatedDate     time.Time `json:"createdDate"`
	LastModified    time.Time `json:"lastModified"`
	Status          string    `json:"status"`
	AudioFileCount  int       `json:"audioFileCount"`
	IsTextExtracted bool      `json:"isTextExtracted"`
	IsTranscribed   bool      `json:"isTranscribed"`
	IsAligned       bool      `json:"isAligned"`
}

// ProjectResponse represents a single project response
type ProjectResponse struct {
	Project *Project `json:"project"`
}

// ProjectsResponse represents multiple projects response
type ProjectsResponse struct {
	Projects []Project `json:"projects"`
}

// Book represents book information
type Book struct {
	RowKey      string    `json:"rowKey"`
	Title       string    `json:"title"`
	Author      string    `json:"author"`
	ISBN        string    `json:"isbn"`
	PublishDate time.Time `json:"publishDate"`
	PageCount   int       `json:"pageCount"`
	Language    string    `json:"language"`
}

// BookResponse represents a book response
type BookResponse struct {
	Book *Book `json:"book"`
}

// AudioFile represents an audio file in the system
type AudioFile struct {
	PartitionKey string    `json:"partitionKey"`
	RowKey       string    `json:"rowKey"`
	FileName     string    `json:"fileName"`
	FilePath     string    `json:"filePath"`
	Duration     float64   `json:"duration"`
	FileSize     int64     `json:"fileSize"`
	Format       string    `json:"format"`
	UploadDate   time.Time `json:"uploadDate"`
	IsProcessed  bool      `json:"isProcessed"`
}

// AudioFileResponse represents a single audio file response
type AudioFileResponse struct {
	AudioFile *AudioFile `json:"audioFile"`
}

// AudioFilesResponse represents multiple audio files response
type AudioFilesResponse struct {
	AudioFiles []AudioFile `json:"audioFiles"`
}

// AudioFileListResponse represents a list of audio file names
type AudioFileListResponse struct {
	FileNames []string `json:"fileNames"`
}

// AlignmentItem represents an alignment item
type AlignmentItem struct {
	ID          string    `json:"id"`
	AudioFileID string    `json:"audioFileID"`
	Category    string    `json:"category"`
	StartTime   float64   `json:"startTime"`
	EndTime     float64   `json:"endTime"`
	Text        string    `json:"text"`
	Confidence  float64   `json:"confidence"`
	IsConfirmed bool      `json:"isConfirmed"`
	CreatedDate time.Time `json:"createdDate"`
}

// AlignmentItemsResponse represents multiple alignment items response
type AlignmentItemsResponse struct {
	AlignmentItems []AlignmentItem `json:"alignmentItems"`
}

// CountResponse represents a count response
type CountResponse struct {
	Count int `json:"count"`
}

// StatusResponse represents a boolean status response
type StatusResponse struct {
	Status bool `json:"status"`
}

// Transcription represents transcription data
type Transcription struct {
	AudioFileID string    `json:"audioFileID"`
	Text        string    `json:"text"`
	Language    string    `json:"language"`
	Confidence  float64   `json:"confidence"`
	WordCount   int       `json:"wordCount"`
	CreatedDate time.Time `json:"createdDate"`
}

// TranscriptionResponse represents a transcription response
type TranscriptionResponse struct {
	Transcription *Transcription `json:"transcription"`
}

// DeleteResponse represents a deletion operation response
type DeleteResponse struct {
	Message string `json:"message"`
}
