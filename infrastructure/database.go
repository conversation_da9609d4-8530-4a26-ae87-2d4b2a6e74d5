// Package infrastructure provides shared database operations for TrackSurfer APIs
package infrastructure

import (
	"encoding/json"
	"fmt"
	"log"
)

// =============================================================================
// SHARED DATABASE UTILITIES
// =============================================================================

// DatabaseUtilities provides shared database operations for both GET and POST APIs
type DatabaseUtilities struct{}

// =============================================================================
// GET OPERATIONS (from crud-api)
// =============================================================================

// GetUser retrieves a specific user by partition key
func (db *DatabaseUtilities) GetUser(partitionKey string) (*TrackSurferUser, error) {
	log.Printf("GetUser called with partitionKey: %s", partitionKey)

	// Return dummy user data (replace with actual database call)
	return &TrackSurferUser{
		PartitionKey: partitionKey,
		RowKey:       "user_001",
		Name:         "John Doe",
		Email:        "<EMAIL>",
		IsActive:     true,
	}, nil
}

// GetAllUsers retrieves all users
func (db *DatabaseUtilities) GetAllUsers() ([]TrackSurferUser, error) {
	log.Printf("GetAllUsers called")

	return []TrackSurferUser{
		{
			PartitionKey: "user_partition_1",
			RowKey:       "user_001",
			Name:         "John Doe",
			Email:        "<EMAIL>",
			IsActive:     true,
		},
		{
			PartitionKey: "user_partition_2",
			RowKey:       "user_002",
			Name:         "Jane Smith",
			Email:        "<EMAIL>",
			IsActive:     true,
		},
	}, nil
}

// GetProject retrieves a specific project by row key
func (db *DatabaseUtilities) GetProject(projectRowKey string) (*Project, error) {
	log.Printf("GetProject called with projectRowKey: %s", projectRowKey)

	return &Project{
		PartitionKey:    "project_partition_1",
		RowKey:          projectRowKey,
		Name:            "Sample Audio Book Project",
		Description:     "A project for processing audio book files and creating transcriptions",
		Status:          "In Progress",
		AudioFileCount:  5,
		IsTextExtracted: true,
		IsTranscribed:   true,
		IsAligned:       false,
	}, nil
}

// GetAllProjects retrieves all projects
func (db *DatabaseUtilities) GetAllProjects() ([]Project, error) {
	log.Printf("GetAllProjects called")

	return []Project{
		{
			PartitionKey:    "project_partition_1",
			RowKey:          "project_001",
			Name:            "Audio Book Project 1",
			Description:     "First audio book processing project",
			Status:          "Completed",
			AudioFileCount:  8,
			IsTextExtracted: true,
			IsTranscribed:   true,
			IsAligned:       true,
		},
		{
			PartitionKey:    "project_partition_2",
			RowKey:          "project_002",
			Name:            "Audio Book Project 2",
			Description:     "Second audio book processing project",
			Status:          "In Progress",
			AudioFileCount:  5,
			IsTextExtracted: true,
			IsTranscribed:   false,
			IsAligned:       false,
		},
	}, nil
}

// GetBook retrieves book information by project row key
func (db *DatabaseUtilities) GetBook(projectRowKey string) (*Book, error) {
	log.Printf("GetBook called with projectRowKey: %s", projectRowKey)

	return &Book{
		RowKey:      projectRowKey,
		Title:       "The Great Adventure",
		Author:      "Sample Author",
		ISBN:        "978-0123456789",
		PageCount:   350,
		Language:    "English",
	}, nil
}

// GetAudioFiles retrieves audio files for a project
func (db *DatabaseUtilities) GetAudioFiles(projectRowKey string) ([]AudioFile, error) {
	log.Printf("GetAudioFiles called with projectRowKey: %s", projectRowKey)

	return []AudioFile{
		{
			PartitionKey: projectRowKey,
			RowKey:       "audio_001",
			FileName:     "chapter_01.mp3",
			FilePath:     "/audio/projects/" + projectRowKey + "/chapter_01.mp3",
			Duration:     1800.5,
			FileSize:     25600000,
			Format:       "MP3",
			IsProcessed:  true,
		},
		{
			PartitionKey: projectRowKey,
			RowKey:       "audio_002",
			FileName:     "chapter_02.mp3",
			FilePath:     "/audio/projects/" + projectRowKey + "/chapter_02.mp3",
			Duration:     1650.3,
			FileSize:     23400000,
			Format:       "MP3",
			IsProcessed:  true,
		},
	}, nil
}

// GetCountOfProjectAudioFiles gets the count of audio files for a project
func (db *DatabaseUtilities) GetCountOfProjectAudioFiles(projectFolder string) (int, error) {
	log.Printf("GetCountOfProjectAudioFiles called with projectFolder: %s", projectFolder)
	return 5, nil
}

// GetListOfProjectAudioFiles gets the list of audio file names for a project
func (db *DatabaseUtilities) GetListOfProjectAudioFiles(projectRowKey string) ([]string, error) {
	log.Printf("GetListOfProjectAudioFiles called with projectRowKey: %s", projectRowKey)

	return []string{
		"chapter_01.mp3",
		"chapter_02.mp3",
		"chapter_03.mp3",
		"chapter_04.mp3",
		"chapter_05.mp3",
	}, nil
}

// GetAlignmentItems retrieves alignment items for audio file and category
func (db *DatabaseUtilities) GetAlignmentItems(audioFileRowKey, category string) ([]AlignmentItem, error) {
	log.Printf("GetAlignmentItems called with audioFileRowKey: %s, category: %s", audioFileRowKey, category)

	return []AlignmentItem{
		{
			ID:          "align_001",
			AudioFileID: audioFileRowKey,
			Category:    category,
			StartTime:   0.0,
			EndTime:     2.5,
			Text:        "In the beginning",
			Confidence:  0.95,
			IsConfirmed: true,
		},
		{
			ID:          "align_002",
			AudioFileID: audioFileRowKey,
			Category:    category,
			StartTime:   2.5,
			EndTime:     5.8,
			Text:        "there was a great adventure",
			Confidence:  0.92,
			IsConfirmed: false,
		},
	}, nil
}

// GetCountOfProjectAlignmentItems gets count of alignment items for a project and category
func (db *DatabaseUtilities) GetCountOfProjectAlignmentItems(projectPartitionKey, category string) (int, error) {
	log.Printf("GetCountOfProjectAlignmentItems called with projectPartitionKey: %s, category: %s", projectPartitionKey, category)
	return 10, nil
}

// GetCountOfConfirmedAlignmentItems gets count of confirmed alignment items
func (db *DatabaseUtilities) GetCountOfConfirmedAlignmentItems(projectPartitionKey string) (int, error) {
	log.Printf("GetCountOfConfirmedAlignmentItems called with projectPartitionKey: %s", projectPartitionKey)
	return 8, nil
}

// GetTranscription retrieves transcription for an audio file
func (db *DatabaseUtilities) GetTranscription(audioFileRowKey string) ([]AlignmentItem, error) {
	log.Printf("GetTranscription called with audioFileRowKey: %s", audioFileRowKey)

	return []AlignmentItem{
		{
			ID:          "word_001",
			AudioFileID: audioFileRowKey,
			Category:    "Word",
			StartTime:   0.0,
			EndTime:     0.5,
			Text:        "In",
			Confidence:  0.98,
			IsConfirmed: true,
		},
		{
			ID:          "word_002",
			AudioFileID: audioFileRowKey,
			Category:    "Word",
			StartTime:   0.5,
			EndTime:     0.8,
			Text:        "the",
			Confidence:  0.97,
			IsConfirmed: true,
		},
	}, nil
}

// =============================================================================
// STATUS CHECK OPERATIONS
// =============================================================================

// IsTextExtracted checks if text is extracted for a project
func (db *DatabaseUtilities) IsTextExtracted(projectRowKey string) (bool, error) {
	log.Printf("IsTextExtracted called with projectRowKey: %s", projectRowKey)
	return true, nil
}

// IsTranscribed checks if project is transcribed
func (db *DatabaseUtilities) IsTranscribed(projectRowKey string) (bool, error) {
	log.Printf("IsTranscribed called with projectRowKey: %s", projectRowKey)
	return true, nil
}

// IsAlignmentComplete checks if project alignment is complete
func (db *DatabaseUtilities) IsAlignmentComplete(projectRowKey string) (bool, error) {
	log.Printf("IsAlignmentComplete called with projectRowKey: %s", projectRowKey)
	return false, nil
}

// IsTranscoded checks if project is transcoded
func (db *DatabaseUtilities) IsTranscoded(projectRowKey string) (bool, error) {
	log.Printf("IsTranscoded called with projectRowKey: %s", projectRowKey)
	return true, nil
}

// IsAudioUploaded checks if audio is uploaded for a project
func (db *DatabaseUtilities) IsAudioUploaded(projectRowKey string) (bool, error) {
	log.Printf("IsAudioUploaded called with projectRowKey: %s", projectRowKey)
	return true, nil
}

// =============================================================================
// DELETE OPERATIONS
// =============================================================================

// DeleteAllAlignmentItemsForAudioFile deletes alignment items for a specific audio file
func (db *DatabaseUtilities) DeleteAllAlignmentItemsForAudioFile(audioFilePartitionKey, audioFileRowKey string, includeTranscription bool) (int, error) {
	log.Printf("DeleteAllAlignmentItemsForAudioFile called with audioFilePartitionKey: %s, audioFileRowKey: %s, includeTranscription: %t",
		audioFilePartitionKey, audioFileRowKey, includeTranscription)
	return 15, nil
}

// DeleteTranscription deletes transcription for a project
func (db *DatabaseUtilities) DeleteTranscription(projectRowKey string) (int, error) {
	log.Printf("DeleteTranscription called with projectRowKey: %s", projectRowKey)
	return 150, nil
}

// DeleteAllAlignmentItems deletes all alignment items for a project
func (db *DatabaseUtilities) DeleteAllAlignmentItems(projectRowKey string, includeTranscription bool) (int, error) {
	log.Printf("DeleteAllAlignmentItems called with projectRowKey: %s, includeTranscription: %t", projectRowKey, includeTranscription)
	return 25, nil
}

// =============================================================================
// HELPER FUNCTIONS FOR COUNT OPERATIONS
// =============================================================================

// GetAlignmentItemCount gets total alignment item count for a project
func (db *DatabaseUtilities) GetAlignmentItemCount(projectPartitionKey string) (int, error) {
	return db.GetCountOfProjectAlignmentItems(projectPartitionKey, "All")
}

// GetOmissionCount gets omission count for a project
func (db *DatabaseUtilities) GetOmissionCount(projectPartitionKey string) (int, error) {
	return db.GetCountOfProjectAlignmentItems(projectPartitionKey, "Omissions")
}

// GetInsertionCount gets insertion count for a project
func (db *DatabaseUtilities) GetInsertionCount(projectPartitionKey string) (int, error) {
	return db.GetCountOfProjectAlignmentItems(projectPartitionKey, "Insertions")
}

// =============================================================================
// POST OPERATIONS (from post-api)
// =============================================================================

// QueueTextExtraction queues a text extraction request
func (db *DatabaseUtilities) QueueTextExtraction(rowKey, method string) (string, string, error) {
	log.Printf("QueueTextExtraction called with rowKey: %s, method: %s", rowKey, method)

	request := map[string]interface{}{
		"method": method,
		"rowKey": rowKey,
	}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal request: %v", err)
	}

	message := fmt.Sprintf("Text extraction request for project row key %s has been queued successfully", rowKey)
	return message, string(requestBody), nil
}

// QueueTranscoding queues a transcoding request
func (db *DatabaseUtilities) QueueTranscoding(audioFileID, method string) (string, string, error) {
	log.Printf("QueueTranscoding called with audioFileID: %s, method: %s", audioFileID, method)

	request := map[string]interface{}{
		"method":      method,
		"audioFileID": audioFileID,
	}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal request: %v", err)
	}

	message := fmt.Sprintf("Transcode request for audio file %s has been queued successfully", audioFileID)
	return message, string(requestBody), nil
}

// QueuePickupPackCreation queues a pickup pack creation request
func (db *DatabaseUtilities) QueuePickupPackCreation(audioFileID, packType string) (string, string, error) {
	log.Printf("QueuePickupPackCreation called with audioFileID: %s, packType: %s", audioFileID, packType)

	request := map[string]interface{}{
		"method":      "createPickups",
		"audioFileID": audioFileID,
		"packType":    packType,
	}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal request: %v", err)
	}

	var message string
	if packType == "full" {
		message = fmt.Sprintf("Full pickup pack request for audio file %s has been queued successfully", audioFileID)
	} else {
		message = fmt.Sprintf("Partial pickup pack request for audio file %s has been queued successfully", audioFileID)
	}

	return message, string(requestBody), nil
}

// QueueTranscription queues a transcription request
func (db *DatabaseUtilities) QueueTranscription(audioFileID, transcriber string, usePostProcessing bool, userID string) (string, string, error) {
	log.Printf("QueueTranscription called with audioFileID: %s, transcriber: %s, usePostProcessing: %t, userID: %s",
		audioFileID, transcriber, usePostProcessing, userID)

	// TODO: Get actual project information from database
	projectTitle := "Sample Project"
	projectRowKey := "project_001"

	request := map[string]interface{}{
		"method":           "transcribe",
		"audioFileID":      audioFileID,
		"projectTitle":     projectTitle,
		"projectRowKey":    projectRowKey,
		"transcriber":      transcriber,
		"usePostProcessing": usePostProcessing,
	}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal request: %v", err)
	}

	message := fmt.Sprintf("Transcription request for audio file %s has been queued successfully", audioFileID)
	return message, string(requestBody), nil
}

// QueueAlignment queues an alignment request
func (db *DatabaseUtilities) QueueAlignment(audioFileID string) (string, string, error) {
	log.Printf("QueueAlignment called with audioFileID: %s", audioFileID)

	// TODO: Validate audio file and get project information
	projectTitle := "Sample Project"
	projectRowKey := "project_001"

	request := map[string]interface{}{
		"method":        "aligntranscription",
		"audioFileID":   audioFileID,
		"projectTitle":  projectTitle,
		"projectRowKey": projectRowKey,
	}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal request: %v", err)
	}

	message := fmt.Sprintf("Alignment request for audio file %s has been queued successfully", audioFileID)
	return message, string(requestBody), nil
}

// =============================================================================
// CRUD OPERATIONS
// =============================================================================

// SaveProject saves or updates a project
func (db *DatabaseUtilities) SaveProject(project *Project) error {
	log.Printf("SaveProject called with project: %+v", project)
	// TODO: Implement actual database save
	return nil
}

// SaveAudioFile saves or updates an audio file
func (db *DatabaseUtilities) SaveAudioFile(audioFile *AudioFile) error {
	log.Printf("SaveAudioFile called with audioFile: %+v", audioFile)
	// TODO: Implement actual database save
	return nil
}

// SaveAlignmentItem saves or updates an alignment item
func (db *DatabaseUtilities) SaveAlignmentItem(alignmentItem *AlignmentItem) error {
	log.Printf("SaveAlignmentItem called with alignmentItem: %+v", alignmentItem)
	// TODO: Implement actual database save
	return nil
}

// SaveBook saves or updates a book
func (db *DatabaseUtilities) SaveBook(book *Book) error {
	log.Printf("SaveBook called with book: %+v", book)
	// TODO: Implement actual database save
	return nil
}

// SaveUser saves or updates a user
func (db *DatabaseUtilities) SaveUser(user *TrackSurferUser) error {
	log.Printf("SaveUser called with user: %+v", user)
	// TODO: Implement actual database save
	return nil
}

// DeleteProjectAndData deletes a project and all associated data
func (db *DatabaseUtilities) DeleteProjectAndData(rowKey string) error {
	log.Printf("DeleteProjectAndData called with rowKey: %s", rowKey)
	// TODO: Implement actual database deletion with cleanup
	return nil
}

// DeleteAudioFileAndData deletes an audio file and associated data
func (db *DatabaseUtilities) DeleteAudioFileAndData(partitionKey, rowKey string) error {
	log.Printf("DeleteAudioFileAndData called with partitionKey: %s, rowKey: %s", partitionKey, rowKey)
	// TODO: Implement actual database deletion with blob cleanup
	return nil
}

// DeleteAlignmentItem deletes a single alignment item
func (db *DatabaseUtilities) DeleteAlignmentItem(partitionKey, rowKey string) error {
	log.Printf("DeleteAlignmentItem called with partitionKey: %s, rowKey: %s", partitionKey, rowKey)
	// TODO: Implement actual database deletion
	return nil
}

// DeleteBook deletes a book
func (db *DatabaseUtilities) DeleteBook(partitionKey, rowKey string) error {
	log.Printf("DeleteBook called with partitionKey: %s, rowKey: %s", partitionKey, rowKey)
	// TODO: Implement actual database deletion
	return nil
}

// DeleteUser deletes a user
func (db *DatabaseUtilities) DeleteUser(partitionKey, rowKey string) error {
	log.Printf("DeleteUser called with partitionKey: %s, rowKey: %s", partitionKey, rowKey)
	// TODO: Implement actual database deletion
	return nil
}

// DeleteMultipleProjects deletes multiple projects with permission checking
func (db *DatabaseUtilities) DeleteMultipleProjects(projectRowKeys []string, userID string) error {
	log.Printf("DeleteMultipleProjects called with projectRowKeys: %v, userID: %s", projectRowKeys, userID)
	// TODO: Implement bulk deletion with permission checking
	return nil
}

// DeleteAllProjects deletes all projects in the system
func (db *DatabaseUtilities) DeleteAllProjects() error {
	log.Printf("DeleteAllProjects called")
	// TODO: Implement bulk deletion of all projects
	return nil
}

// Global database instance
var DbUtils = &DatabaseUtilities{}
