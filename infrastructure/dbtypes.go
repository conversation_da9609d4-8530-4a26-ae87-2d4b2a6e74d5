// Package infrastructure provides shared database types for TrackSurfer APIs
package infrastructure

import "time"

// =============================================================================
// SHARED DATABASE ENTITY TYPES
// =============================================================================

// TrackSurferUser represents a TrackSurfer user
type TrackSurferUser struct {
	PartitionKey     string    `json:"partitionKey"`
	RowKey           string    `json:"rowKey"`
	Name             string    `json:"name"`
	Email            string    `json:"email"`
	IsActive         bool      `json:"isActive"`
	Role             string    `json:"role,omitempty"`
	StripeCustomerId string    `json:"stripeCustomerId,omitempty"`
	CreatedAt        time.Time `json:"createdAt,omitempty"`
	UpdatedAt        time.Time `json:"updatedAt,omitempty"`
}

// Project represents a TrackSurfer project
type Project struct {
	PartitionKey    string    `json:"partitionKey"`
	RowKey          string    `json:"rowKey"`
	Name            string    `json:"name"`
	Title           string    `json:"title,omitempty"`
	Description     string    `json:"description,omitempty"`
	Status          string    `json:"status"`
	AudioFileCount  int       `json:"audioFileCount"`
	IsTextExtracted bool      `json:"isTextExtracted"`
	IsTranscribed   bool      `json:"isTranscribed"`
	IsAligned       bool      `json:"isAligned"`
	CreatedAt       time.Time `json:"createdAt,omitempty"`
	UpdatedAt       time.Time `json:"updatedAt,omitempty"`
}

// AudioFile represents an audio file in the system
type AudioFile struct {
	PartitionKey string    `json:"partitionKey"`
	RowKey       string    `json:"rowKey"`
	FileName     string    `json:"fileName"`
	FilePath     string    `json:"filePath"`
	Duration     float64   `json:"duration"`
	FileSize     int64     `json:"fileSize"`
	Format       string    `json:"format"`
	IsProcessed  bool      `json:"isProcessed"`
	StartPage    *int      `json:"startPage,omitempty"`
	EndPage      *int      `json:"endPage,omitempty"`
	CreatedAt    time.Time `json:"createdAt,omitempty"`
	UpdatedAt    time.Time `json:"updatedAt,omitempty"`
}

// AlignmentItem represents an alignment item for audio-text synchronization
type AlignmentItem struct {
	ID          string  `json:"id"`
	AudioFileID string  `json:"audioFileID"`
	Category    string  `json:"category"`
	StartTime   float64 `json:"startTime"`
	EndTime     float64 `json:"endTime"`
	Text        string  `json:"text"`
	Confidence  float64 `json:"confidence"`
	IsConfirmed bool    `json:"isConfirmed"`
}

// Book represents book information
type Book struct {
	RowKey    string `json:"rowKey"`
	Title     string `json:"title"`
	Author    string `json:"author"`
	ISBN      string `json:"isbn,omitempty"`
	PageCount int    `json:"pageCount,omitempty"`
	Language  string `json:"language,omitempty"`
}

// Transcription represents transcription data
type Transcription struct {
	AudioFileID string  `json:"audioFileID"`
	Text        string  `json:"text"`
	Language    string  `json:"language"`
	Confidence  float64 `json:"confidence"`
	WordCount   int     `json:"wordCount"`
}

// =============================================================================
// PROCESSING STATUS TYPES
// =============================================================================

// ProcessingStatus represents the status of a processing operation
type ProcessingStatus struct {
	Status    string    `json:"status"`
	Message   string    `json:"message,omitempty"`
	Progress  float64   `json:"progress,omitempty"`
	StartedAt time.Time `json:"startedAt,omitempty"`
	UpdatedAt time.Time `json:"updatedAt,omitempty"`
}

// QueueStatus represents the status of a queue operation
type QueueStatus struct {
	Queued    bool      `json:"queued"`
	QueuedAt  time.Time `json:"queuedAt,omitempty"`
	Message   string    `json:"message,omitempty"`
	RequestID string    `json:"requestId,omitempty"`
}

// =============================================================================
// ERROR TYPES
// =============================================================================

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// ProcessingError represents a processing error
type ProcessingError struct {
	Code      string    `json:"code"`
	Message   string    `json:"message"`
	Details   string    `json:"details,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

// EntityMetadata represents common metadata for entities
type EntityMetadata struct {
	CreatedBy string    `json:"createdBy,omitempty"`
	UpdatedBy string    `json:"updatedBy,omitempty"`
	Version   int       `json:"version,omitempty"`
	ETag      string    `json:"etag,omitempty"`
	CreatedAt time.Time `json:"createdAt,omitempty"`
	UpdatedAt time.Time `json:"updatedAt,omitempty"`
}

// BulkOperationResult represents the result of a bulk operation
type BulkOperationResult struct {
	TotalItems     int      `json:"totalItems"`
	ProcessedItems int      `json:"processedItems"`
	FailedItems    int      `json:"failedItems"`
	Errors         []string `json:"errors,omitempty"`
	Message        string   `json:"message"`
}

// FileUploadInfo represents file upload information
type FileUploadInfo struct {
	FileName     string    `json:"fileName"`
	FileSize     int64     `json:"fileSize"`
	ContentType  string    `json:"contentType"`
	UploadedAt   time.Time `json:"uploadedAt"`
	StoragePath  string    `json:"storagePath"`
	DownloadURL  string    `json:"downloadUrl,omitempty"`
}

// =============================================================================
// DATABASE CONFIGURATION TYPES
// =============================================================================

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	ConnectionString string `json:"connectionString"`
	TablePrefix      string `json:"tablePrefix,omitempty"`
	RetryAttempts    int    `json:"retryAttempts,omitempty"`
	TimeoutSeconds   int    `json:"timeoutSeconds,omitempty"`
}

// TableNames represents the names of database tables
type TableNames struct {
	Users          string `json:"users"`
	Projects       string `json:"projects"`
	AudioFiles     string `json:"audioFiles"`
	AlignmentItems string `json:"alignmentItems"`
	Books          string `json:"books"`
}

// DefaultTableNames returns the default table names
func DefaultTableNames() TableNames {
	return TableNames{
		Users:          "users",
		Projects:       "projects",
		AudioFiles:     "audiofiles",
		AlignmentItems: "alignments",
		Books:          "books",
	}
}

// =============================================================================
// QUERY TYPES
// =============================================================================

// QueryFilter represents a filter for database queries
type QueryFilter struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // eq, ne, gt, lt, ge, le, contains
	Value    interface{} `json:"value"`
}

// QueryOptions represents options for database queries
type QueryOptions struct {
	Filters   []QueryFilter `json:"filters,omitempty"`
	OrderBy   string        `json:"orderBy,omitempty"`
	OrderDesc bool          `json:"orderDesc,omitempty"`
	Limit     int           `json:"limit,omitempty"`
	Offset    int           `json:"offset,omitempty"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	Page       int `json:"page"`
	PageSize   int `json:"pageSize"`
	TotalItems int `json:"totalItems"`
	TotalPages int `json:"totalPages"`
	HasNext    bool `json:"hasNext"`
	HasPrev    bool `json:"hasPrev"`
}
