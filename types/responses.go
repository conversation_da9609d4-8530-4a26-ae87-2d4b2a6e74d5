// Package types provide shared response types for TrackSurfer APIs
package types

import "encore.app/infrastructure"

// =============================================================================
// GET API RESPONSE TYPES (CRUD/Read Operations)
// =============================================================================

// ===== USER MANAGEMENT RESPONSES =====

// UserResponse for single user responses (GetUser)
type UserResponse struct {
	User *infrastructure.TrackSurferUser `json:"user"`
}

// UsersResponse for multiple users responses (GetUsers)
type UsersResponse struct {
	Users []infrastructure.TrackSurferUser `json:"users"`
}

// ===== PROJECT MANAGEMENT RESPONSES =====

// ProjectResponse for single project responses (GetProject)
type ProjectResponse struct {
	Project *infrastructure.Project `json:"project"`
}

// ProjectsResponse for multiple projects responses (GetAllProjects)
type ProjectsResponse struct {
	Projects []infrastructure.Project `json:"projects"`
}

// BookResponse for book responses (GetBook)
type BookResponse struct {
	Book *infrastructure.Book `json:"book"`
}

// ===== AUDIO FILE MANAGEMENT RESPONSES =====

// AudioFileResponse for single audio file responses (GetAudioFile)
type AudioFileResponse struct {
	AudioFile *infrastructure.AudioFile `json:"audioFile"`
}

// AudioFilesResponse for multiple audio files responses (GetAudioFiles)
type AudioFilesResponse struct {
	AudioFiles []infrastructure.AudioFile `json:"audioFiles"`
}

// AudioFileListResponse for audio file name lists (GetListOfProjectAudioFiles)
type AudioFileListResponse struct {
	FileNames []string `json:"fileNames"`
}

// ===== ALIGNMENT ITEMS RESPONSES =====

// AlignmentItemsResponse for alignment items responses (GetAlignmentItems)
type AlignmentItemsResponse struct {
	AlignmentItems []infrastructure.AlignmentItem `json:"alignmentItems"`
}

// ===== TRANSCRIPTION RESPONSES =====

// TranscriptionResponse for transcription responses (GetTranscription)
type TranscriptionResponse struct {
	Transcription *infrastructure.Transcription `json:"transcription"`
}

// ===== COUNT & STATUS RESPONSES =====

// CountResponse for count operations (GetCountOfProjectAudioFiles, GetAlignmentItemCount, etc.)
type CountResponse struct {
	Count int `json:"count"`
}

// StatusResponse for status check operations (IsTextExtracted, IsTranscribed, etc.)
type StatusResponse struct {
	Status bool `json:"status"`
}

// DeleteResponse for delete operations (DeleteAlignmentItems, DeleteTranscription)
type DeleteResponse struct {
	Message string `json:"message"`
}

// =============================================================================
// POST API RESPONSE TYPES (Create/Update/Delete Operations)
// =============================================================================

// ===== GENERAL POST RESPONSES =====

// PostResponse for general POST operations (CRUD operations)
type PostResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success,omitempty"`
}

// ===== QUEUE & PROCESSING RESPONSES =====

// QueueResponse for queue operations (ExtractText, Transcode, Transcribe, etc.)
type QueueResponse struct {
	Message     string `json:"message"`
	RequestBody string `json:"requestBody,omitempty"`
}

// =============================================================================
// BATCH OPERATION RESPONSE TYPES
// =============================================================================

// BatchResponse for batch operations
type BatchResponse struct {
	Message        string                              `json:"message"`
	BulkResult     *infrastructure.BulkOperationResult `json:"bulkResult,omitempty"`
	ProcessedCount int                                 `json:"processedCount"`
	FailedCount    int                                 `json:"failedCount"`
	Errors         []string                            `json:"errors,omitempty"`
}

// BulkDeleteResponse for bulk delete operations
type BulkDeleteResponse struct {
	Message      string   `json:"message"`
	DeletedCount int      `json:"deletedCount"`
	FailedCount  int      `json:"failedCount"`
	FailedItems  []string `json:"failedItems,omitempty"`
	Errors       []string `json:"errors,omitempty"`
}

// =============================================================================
// SEARCH AND FILTER RESPONSE TYPES
// =============================================================================

// SearchResponse for search operations
type SearchResponse struct {
	TotalCount int                            `json:"totalCount"`
	Pagination *infrastructure.PaginationInfo `json:"pagination,omitempty"`
	Query      string                         `json:"query"`
	Filters    []infrastructure.QueryFilter   `json:"filters,omitempty"`
}

// FilterResponse for filter operations
type FilterResponse struct {
	TotalCount int                            `json:"totalCount"`
	Pagination *infrastructure.PaginationInfo `json:"pagination,omitempty"`
	Filters    []infrastructure.QueryFilter   `json:"filters"`
}

// =============================================================================
// FILE UPLOAD RESPONSE TYPES
// =============================================================================

// FileUploadResponse for file upload operations
type FileUploadResponse struct {
	Message     string                         `json:"message"`
	FileInfo    *infrastructure.FileUploadInfo `json:"fileInfo"`
	UploadURL   string                         `json:"uploadUrl,omitempty"`
	DownloadURL string                         `json:"downloadUrl,omitempty"`
}

// AudioFileUploadResponse for audio file upload operations
type AudioFileUploadResponse struct {
	FileUploadResponse
	AudioFile *infrastructure.AudioFile `json:"audioFile,omitempty"`
	Duration  float64                   `json:"duration,omitempty"`
	Format    string                    `json:"format,omitempty"`
}

// =============================================================================
// PROCESSING STATUS RESPONSE TYPES
// =============================================================================

// ProcessingStatusResponse for processing status operations
type ProcessingStatusResponse struct {
	Status              *infrastructure.ProcessingStatus `json:"status"`
	QueueInfo           *infrastructure.QueueStatus      `json:"queueInfo,omitempty"`
	Progress            float64                          `json:"progress"`
	Message             string                           `json:"message,omitempty"`
	EstimatedCompletion string                           `json:"estimatedCompletion,omitempty"`
}

// QueueStatusResponse for queue status operations
type QueueStatusResponse struct {
	QueueInfo     *infrastructure.QueueStatus `json:"queueInfo"`
	Position      int                         `json:"position,omitempty"`
	EstimatedWait string                      `json:"estimatedWait,omitempty"`
}

// =============================================================================
// VALIDATION RESPONSE TYPES
// =============================================================================

// ValidationResponse for validation operations
type ValidationResponse struct {
	IsValid  bool                             `json:"isValid"`
	Errors   []infrastructure.ValidationError `json:"errors,omitempty"`
	Warnings []string                         `json:"warnings,omitempty"`
}

// HealthCheckResponse for health check operations
type HealthCheckResponse struct {
	Status       string            `json:"status"` // "healthy", "degraded", "unhealthy"
	Message      string            `json:"message,omitempty"`
	Checks       map[string]string `json:"checks,omitempty"`
	ResponseTime string            `json:"responseTime,omitempty"`
	Version      string            `json:"version,omitempty"`
}

// =============================================================================
// ERROR RESPONSE TYPES
// =============================================================================

// ErrorResponse for error responses
type ErrorResponse struct {
	Error   string                           `json:"error"`
	Code    string                           `json:"code,omitempty"`
	Details string                           `json:"details,omitempty"`
	Errors  []infrastructure.ValidationError `json:"errors,omitempty"`
}

// =============================================================================
// ANALYTICS RESPONSE TYPES
// =============================================================================

// AnalyticsResponse for analytics operations
type AnalyticsResponse struct {
	Metrics     map[string]interface{} `json:"metrics"`
	TimeRange   string                 `json:"timeRange,omitempty"`
	GeneratedAt string                 `json:"generatedAt"`
}

// StatsResponse for statistics operations
type StatsResponse struct {
	TotalUsers            int     `json:"totalUsers"`
	TotalProjects         int     `json:"totalProjects"`
	TotalAudioFiles       int     `json:"totalAudioFiles"`
	TotalAlignmentItems   int     `json:"totalAlignmentItems"`
	ProcessingQueue       int     `json:"processingQueue"`
	AverageProcessingTime float64 `json:"averageProcessingTime"`
	SystemLoad            float64 `json:"systemLoad,omitempty"`
}

// =============================================================================
// PAGINATION RESPONSE WRAPPER
// =============================================================================

// PaginatedResponse wraps any response with pagination information
type PaginatedResponse struct {
	Pagination *infrastructure.PaginationInfo `json:"pagination"`
	TotalCount int                            `json:"totalCount"`
}

// =============================================================================
// GENERIC RESPONSE WRAPPER
// =============================================================================

// APIResponse is a generic wrapper for all API responses
type APIResponse struct {
	Success   bool           `json:"success"`
	Error     *ErrorResponse `json:"error,omitempty"`
	Message   string         `json:"message,omitempty"`
	RequestID string         `json:"requestId,omitempty"`
	Timestamp string         `json:"timestamp"`
}
