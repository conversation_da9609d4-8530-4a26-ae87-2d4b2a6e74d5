// Package types provides shared request and response types for TrackSurfer APIs
package types

import "encore.app/infrastructure"

// =============================================================================
// CRUD API REQUEST TYPES (GET operations)
// =============================================================================

// GetUserRequest for retrieving a specific user
type GetUserRequest struct {
	PartitionKey string `json:"partitionKey"`
}

// GetProjectRequest for retrieving a specific project
type GetProjectRequest struct {
	RowKey string `json:"rowKey"`
}

// GetBookRequest for retrieving book information
type GetBookRequest struct {
	RowKey string `json:"rowKey"`
}

// GetCountRequest for count operations
type GetCountRequest struct {
	PartitionKey string `json:"partitionKey"`
}

// GetListRequest for list operations
type GetListRequest struct {
	PartitionKey string `json:"partitionKey"`
}

// GetAudioFilesRequest for retrieving audio files
type GetAudioFilesRequest struct {
	PartitionKey string `json:"partitionKey"`
}

// GetAudioFileRequest for retrieving a specific audio file
type GetAudioFileRequest struct {
	PartitionKey string `json:"partitionKey"`
	RowKey       string `json:"rowKey"`
}

// GetAlignmentItemsRequest for retrieving alignment items
type GetAlignmentItemsRequest struct {
	AudioFileID string `json:"audioFileID"`
	Category    string `json:"category"`
}

// GetCountAlignmentItemsRequest for counting alignment items
type GetCountAlignmentItemsRequest struct {
	PartitionKey string `json:"partitionKey"`
	Category     string `json:"category"`
}

// GetTranscriptionRequest for retrieving transcription
type GetTranscriptionRequest struct {
	AudioFileID string `json:"audioFileID"`
}

// =============================================================================
// POST API REQUEST TYPES
// =============================================================================

// BasePostRequest contains common fields for all POST requests
type BasePostRequest struct {
	Method string `json:"method"`
	UserID string `json:"userID,omitempty"`
}

// ExtractTextRequest for text extraction operations
type ExtractTextRequest struct {
	Method string `json:"method"`
	RowKey string `json:"rowKey"`
	UserID string `json:"userID,omitempty"`
}

// TranscodeRequest for audio transcoding operations
type TranscodeRequest struct {
	Method      string `json:"method"`
	AudioFileID string `json:"audioFileID"`
	UserID      string `json:"userID,omitempty"`
}

// RequestPickupPackRequest for pickup pack creation
type RequestPickupPackRequest struct {
	Method      string `json:"method"`
	AudioFileID string `json:"audioFileID"`
	PackType    string `json:"packType"` // "full" or "partial"
	UserID      string `json:"userID,omitempty"`
}

// TranscribeRequest for transcription operations
type TranscribeRequest struct {
	Method            string `json:"method"`
	AudioFileID       string `json:"audioFileID"`
	Transcriber       string `json:"transcriber,omitempty"`
	UsePostProcessing bool   `json:"usePostProcessing,omitempty"`
	UserID            string `json:"userID,omitempty"`
}

// AlignTranscriptionRequest for alignment operations
type AlignTranscriptionRequest struct {
	Method      string `json:"method"`
	AudioFileID string `json:"audioFileID,omitempty"`
	RowKey      string `json:"rowKey,omitempty"`
	UserID      string `json:"userID,omitempty"`
}

// DeleteProjectsRequest for bulk project deletion
type DeleteProjectsRequest struct {
	Method         string   `json:"method"`
	ProjectRowKeys []string `json:"projectRowKeys"`
	UserID         string   `json:"userID,omitempty"`
}

// ProjectRequest for project CRUD operations
type ProjectRequest struct {
	Method       string                     `json:"method"`
	UserID       string                     `json:"userID,omitempty"`
	Project      *infrastructure.Project    `json:"project,omitempty"`
	PartitionKey string                     `json:"partitionKey,omitempty"`
	RowKey       string                     `json:"rowKey,omitempty"`
}

// AudioFileRequest for audio file CRUD operations
type AudioFileRequest struct {
	Method       string                      `json:"method"`
	UserID       string                      `json:"userID,omitempty"`
	AudioFile    *infrastructure.AudioFile   `json:"audioFile,omitempty"`
	PartitionKey string                      `json:"partitionKey,omitempty"`
	RowKey       string                      `json:"rowKey,omitempty"`
}

// AlignmentItemRequest for alignment item CRUD operations
type AlignmentItemRequest struct {
	Method        string                          `json:"method"`
	UserID        string                          `json:"userID,omitempty"`
	AlignmentItem *infrastructure.AlignmentItem  `json:"alignmentItem,omitempty"`
	PartitionKey  string                          `json:"partitionKey,omitempty"`
	RowKey        string                          `json:"rowKey,omitempty"`
}

// BookRequest for book CRUD operations
type BookRequest struct {
	Method       string                 `json:"method"`
	UserID       string                 `json:"userID,omitempty"`
	Book         *infrastructure.Book   `json:"book,omitempty"`
	PartitionKey string                 `json:"partitionKey,omitempty"`
	RowKey       string                 `json:"rowKey,omitempty"`
}

// UserRequest for user CRUD operations
type UserRequest struct {
	Method       string                           `json:"method"`
	UserID       string                           `json:"userID,omitempty"`
	User         *infrastructure.TrackSurferUser `json:"user,omitempty"`
	PartitionKey string                           `json:"partitionKey,omitempty"`
	RowKey       string                           `json:"rowKey,omitempty"`
}

// =============================================================================
// BATCH REQUEST TYPES
// =============================================================================

// BatchRequest represents a batch operation request
type BatchRequest struct {
	Method    string        `json:"method"`
	UserID    string        `json:"userID,omitempty"`
	Items     []interface{} `json:"items"`
	BatchSize int           `json:"batchSize,omitempty"`
}

// BulkDeleteRequest for bulk deletion operations
type BulkDeleteRequest struct {
	Method       string   `json:"method"`
	UserID       string   `json:"userID,omitempty"`
	TableName    string   `json:"tableName"`
	ItemKeys     []string `json:"itemKeys"`
	ForceDelete  bool     `json:"forceDelete,omitempty"`
}

// =============================================================================
// SEARCH AND FILTER REQUEST TYPES
// =============================================================================

// SearchRequest for search operations
type SearchRequest struct {
	Query        string                           `json:"query"`
	SearchFields []string                         `json:"searchFields,omitempty"`
	Filters      []infrastructure.QueryFilter    `json:"filters,omitempty"`
	Options      *infrastructure.QueryOptions    `json:"options,omitempty"`
}

// FilterRequest for filtering operations
type FilterRequest struct {
	TableName string                           `json:"tableName"`
	Filters   []infrastructure.QueryFilter    `json:"filters"`
	Options   *infrastructure.QueryOptions    `json:"options,omitempty"`
}

// =============================================================================
// FILE UPLOAD REQUEST TYPES
// =============================================================================

// FileUploadRequest for file upload operations
type FileUploadRequest struct {
	FileName    string `json:"fileName"`
	ContentType string `json:"contentType"`
	FileSize    int64  `json:"fileSize"`
	ProjectID   string `json:"projectID,omitempty"`
	UserID      string `json:"userID,omitempty"`
}

// AudioFileUploadRequest for audio file uploads
type AudioFileUploadRequest struct {
	FileUploadRequest
	Duration     float64 `json:"duration,omitempty"`
	Format       string  `json:"format,omitempty"`
	SampleRate   int     `json:"sampleRate,omitempty"`
	Channels     int     `json:"channels,omitempty"`
	BitRate      int     `json:"bitRate,omitempty"`
}

// =============================================================================
// VALIDATION REQUEST TYPES
// =============================================================================

// ValidateRequest for validation operations
type ValidateRequest struct {
	EntityType string      `json:"entityType"`
	Data       interface{} `json:"data"`
	Rules      []string    `json:"rules,omitempty"`
}

// HealthCheckRequest for health check operations
type HealthCheckRequest struct {
	CheckDatabase bool `json:"checkDatabase,omitempty"`
	CheckQueues   bool `json:"checkQueues,omitempty"`
	CheckStorage  bool `json:"checkStorage,omitempty"`
}
