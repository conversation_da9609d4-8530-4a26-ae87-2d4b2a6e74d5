package tracksurfer_api

import (
	"context"
	"strings"
	"testing"
)

// Run tests using `encore test`, which compiles the Encore app and then runs `go test`.
// It supports all the same flags that the `go test` command does.
// You automatically get tracing for tests in the local dev dash: http://localhost:9400
// Learn more: https://encore.dev/docs/go/develop/testing

// =============================================================================
// USER MANAGEMENT TESTS
// =============================================================================

func TestGetUser(t *testing.T) {
	req := &GetUserRequest{
		PartitionKey: "test_user_partition",
	}
	resp, err := GetUser(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.User == nil {
		t.Error("Expected user data, got nil")
	}
	if resp.User.PartitionKey != req.PartitionKey {
		t.Errorf("Expected partition key %q, got %q", req.PartitionKey, resp.User.PartitionKey)
	}
	if resp.User.RowKey == "" {
		t.Error("Expected user RowKey to be set")
	}
	if resp.User.Name == "" {
		t.Error("Expected user Name to be set")
	}
	if resp.User.Email == "" {
		t.Error("Expected user Email to be set")
	}
}

func TestGetUserMissingPartitionKey(t *testing.T) {
	req := &GetUserRequest{
		PartitionKey: "",
	}
	_, err := GetUser(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetUsers(t *testing.T) {
	resp, err := GetUsers(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	if len(resp.Users) == 0 {
		t.Error("Expected users data, got empty slice")
	}

	// Verify each user has required fields
	for i, user := range resp.Users {
		if user.PartitionKey == "" {
			t.Errorf("User %d: Expected PartitionKey to be set", i)
		}
		if user.RowKey == "" {
			t.Errorf("User %d: Expected RowKey to be set", i)
		}
		if user.Name == "" {
			t.Errorf("User %d: Expected Name to be set", i)
		}
		if user.Email == "" {
			t.Errorf("User %d: Expected Email to be set", i)
		}
	}
}

// =============================================================================
// PROJECT MANAGEMENT TESTS
// =============================================================================

func TestGetAllProjects(t *testing.T) {
	resp, err := GetAllProjects(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	if len(resp.Projects) == 0 {
		t.Error("Expected projects data, got empty slice")
	}

	// Verify each project has required fields
	for i, project := range resp.Projects {
		if project.PartitionKey == "" {
			t.Errorf("Project %d: Expected PartitionKey to be set", i)
		}
		if project.RowKey == "" {
			t.Errorf("Project %d: Expected RowKey to be set", i)
		}
		if project.Name == "" {
			t.Errorf("Project %d: Expected Name to be set", i)
		}
		if project.Status == "" {
			t.Errorf("Project %d: Expected Status to be set", i)
		}
	}
}

func TestGetProject(t *testing.T) {
	req := &GetProjectRequest{
		RowKey: "test_project_001",
	}
	resp, err := GetProject(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Project == nil {
		t.Error("Expected project data, got nil")
	}
	if resp.Project.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.Project.RowKey)
	}
	if resp.Project.Name == "" {
		t.Error("Expected project Name to be set")
	}
	if resp.Project.Status == "" {
		t.Error("Expected project Status to be set")
	}
}

func TestGetProjectMissingRowKey(t *testing.T) {
	req := &GetProjectRequest{
		RowKey: "",
	}
	_, err := GetProject(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing row key, got nil")
	}
}

func TestGetBook(t *testing.T) {
	req := &GetBookRequest{
		RowKey: "test_project_001",
	}
	resp, err := GetBook(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Book == nil {
		t.Error("Expected book data, got nil")
	}
	if resp.Book.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.Book.RowKey)
	}
	if resp.Book.Title == "" {
		t.Error("Expected book Title to be set")
	}
	if resp.Book.Author == "" {
		t.Error("Expected book Author to be set")
	}
}

func TestGetBookMissingRowKey(t *testing.T) {
	req := &GetBookRequest{
		RowKey: "",
	}
	_, err := GetBook(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing row key, got nil")
	}
}
