package tracksurfer_getapi

import (
	"context"
	"testing"
)

// Run tests using `encore test`, which compiles the Encore app and then runs `go test`.
// It supports all the same flags that the `go test` command does.
// You automatically get tracing for tests in the local dev dash: http://localhost:9400
// Learn more: https://encore.dev/docs/go/develop/testing

func TestGetUser(t *testing.T) {
	req := &GetUserRequest{
		PartitionKey: "test_user_partition",
	}
	resp, err := GetUser(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.User == nil {
		t.Error("Expected user data, got nil")
	}
	if resp.User.PartitionKey != req.PartitionKey {
		t.Errorf("Expected partition key %q, got %q", req.PartitionKey, resp.User.PartitionKey)
	}
}

func TestGetUsers(t *testing.T) {
	resp, err := GetUsers(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	if len(resp.Users) == 0 {
		t.Error("Expected users data, got empty slice")
	}
}

func TestGetProject(t *testing.T) {
	req := &GetProjectRequest{
		RowKey: "test_project_001",
	}
	resp, err := GetProject(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Project == nil {
		t.Error("Expected project data, got nil")
	}
	if resp.Project.RowKey != req.RowKey {
		t.Errorf("Expected row key %q, got %q", req.RowKey, resp.Project.RowKey)
	}
}

func TestGetUserMissingPartitionKey(t *testing.T) {
	req := &GetUserRequest{
		PartitionKey: "",
	}
	_, err := GetUser(context.Background(), req)
	if err == nil {
		t.Error("Expected error for missing partition key, got nil")
	}
}

func TestGetCountOfProjectAudioFiles(t *testing.T) {
	req := &GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := GetCountOfProjectAudioFiles(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Count <= 0 {
		t.Error("Expected positive count, got zero or negative")
	}
}

func TestGetAlignmentItems(t *testing.T) {
	req := &GetAlignmentItemsRequest{
		AudioFileID: "test_audio_001",
		Category:    "All",
	}
	resp, err := GetAlignmentItems(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if len(resp.AlignmentItems) == 0 {
		t.Error("Expected alignment items, got empty slice")
	}
}

func TestIsTextExtracted(t *testing.T) {
	req := &GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := IsTextExtracted(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	// Just verify we get a response - the dummy data returns true
	if resp.Status != true {
		t.Error("Expected status to be true for dummy data")
	}
}

func TestGetTranscription(t *testing.T) {
	req := &GetTranscriptionRequest{
		AudioFileID: "test_audio_001",
	}
	resp, err := GetTranscription(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Transcription == nil {
		t.Error("Expected transcription data, got nil")
	}
	if resp.Transcription.AudioFileID != req.AudioFileID {
		t.Errorf("Expected audio file ID %q, got %q", req.AudioFileID, resp.Transcription.AudioFileID)
	}
}

func TestDeleteAlignmentItems(t *testing.T) {
	req := &GetCountRequest{
		PartitionKey: "test_partition",
	}
	resp, err := DeleteAlignmentItems(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.Message == "" {
		t.Error("Expected deletion message, got empty string")
	}
}
