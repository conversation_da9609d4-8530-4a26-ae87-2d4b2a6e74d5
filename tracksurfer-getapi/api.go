// Package tracksurfer_getapi provides API endpoints for TrackSurfer application
package tracksurfer_getapi

import (
	"context"
	"encore.dev/beta/errs"
)

// User Management Endpoints

// GetUser retrieves a specific user by partition key
//
//encore:api public method=GET path=/api/track-surfer/getuser
func GetUser(ctx context.Context, req *GetUserRequest) (*UserResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing user's partitionKey parameter",
		}
	}

	// Return dummy user data
	return GetDummyUser(req.PartitionKey), nil
}

// GetUsers retrieves all users
//
//encore:api public method=GET path=/api/track-surfer/getusers
func GetUsers(ctx context.Context) (*UsersResponse, error) {
	// Return dummy users data
	return GetDummyUsers(), nil
}

// Project Management Endpoints

// GetAllProjects retrieves all projects
//
//encore:api public method=GET path=/api/track-surfer/getallprojects
func GetAllProjects(ctx context.Context) (*ProjectsResponse, error) {
	// Return dummy projects data
	return GetDummyProjects(), nil
}

// GetProject retrieves a specific project by row key
//
//encore:api public method=GET path=/api/track-surfer/getproject
func GetProject(ctx context.Context, req *GetProjectRequest) (*ProjectResponse, error) {
	if req.RowKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project's rowKey parameter",
		}
	}

	// Return dummy project data
	return GetDummyProject(req.RowKey), nil
}

// GetBook retrieves book information by row key
//
//encore:api public method=GET path=/api/track-surfer/getbook
func GetBook(ctx context.Context, req *GetBookRequest) (*BookResponse, error) {
	if req.RowKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing project's rowKey parameter",
		}
	}

	// Return dummy book data
	return GetDummyBook(req.RowKey), nil
}

// Audio File Management Endpoints

// GetCountOfProjectAudioFiles gets the count of audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getcountofprojectaudiofiles
func GetCountOfProjectAudioFiles(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy count
	return &CountResponse{Count: 5}, nil
}

// GetListOfProjectAudioFiles gets the list of audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getlistofprojectaudiofiles
func GetListOfProjectAudioFiles(ctx context.Context, req *GetListRequest) (*AudioFileListResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy audio file list
	return GetDummyAudioFileList(req.PartitionKey), nil
}

// GetAudioFiles retrieves audio files for a project
//
//encore:api public method=GET path=/api/track-surfer/getaudiofiles
func GetAudioFiles(ctx context.Context, req *GetAudioFilesRequest) (*AudioFilesResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy audio files
	return GetDummyAudioFiles(req.PartitionKey), nil
}

// GetAudioFile retrieves a specific audio file
//
//encore:api public method=GET path=/api/track-surfer/getaudiofile
func GetAudioFile(ctx context.Context, req *GetAudioFileRequest) (*AudioFileResponse, error) {
	if req.RowKey == "" || req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing rowKey or partitionKey parameter",
		}
	}

	// Return dummy audio file
	return GetDummyAudioFile(req.PartitionKey, req.RowKey), nil
}

// Alignment Items Endpoints

// GetAlignmentItems retrieves alignment items for audio file and category
//
//encore:api public method=GET path=/api/track-surfer/getalignmentitems
func GetAlignmentItems(ctx context.Context, req *GetAlignmentItemsRequest) (*AlignmentItemsResponse, error) {
	if req.AudioFileID == "" || req.Category == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Return dummy alignment items
	return GetDummyAlignmentItems(req.AudioFileID, req.Category), nil
}

// GetCountOfProjectAlignmentItems gets count of alignment items for a project and category
//
//encore:api public method=GET path=/api/track-surfer/getcountofprojectalignmentitems
func GetCountOfProjectAlignmentItems(ctx context.Context, req *GetCountAlignmentItemsRequest) (*CountResponse, error) {
	if req.PartitionKey == "" || req.Category == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing projectFolder or category parameter",
		}
	}

	// Return dummy count
	return &CountResponse{Count: 10}, nil
}

// GetConfirmedAlignmentItemCount gets count of confirmed alignment items
//
//encore:api public method=GET path=/api/track-surfer/getconfirmedalignmentitemcount
func GetConfirmedAlignmentItemCount(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy count
	return &CountResponse{Count: 8}, nil
}

// GetAlignmentItemCount gets total alignment item count
//
//encore:api public method=GET path=/api/track-surfer/getalignmentitemcount
func GetAlignmentItemCount(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy count
	return &CountResponse{Count: 15}, nil
}

// GetOmissionCount gets omission count
//
//encore:api public method=GET path=/api/track-surfer/getomissioncount
func GetOmissionCount(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy count
	return &CountResponse{Count: 3}, nil
}

// GetInsertionCount gets insertion count
//
//encore:api public method=GET path=/api/track-surfer/getinsertioncount
func GetInsertionCount(ctx context.Context, req *GetCountRequest) (*CountResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy count
	return &CountResponse{Count: 2}, nil
}

// DeleteAlignmentItems deletes alignment items for a project
//
//encore:api public method=DELETE path=/api/track-surfer/deletealignmentitems
func DeleteAlignmentItems(ctx context.Context, req *GetCountRequest) (*DeleteResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy deletion count
	return &DeleteResponse{Message: "15 alignment items deleted"}, nil
}

// Status Check Endpoints

// IsTextExtracted checks if text is extracted for a project
//
//encore:api public method=GET path=/api/track-surfer/istextextracted
func IsTextExtracted(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy status
	return &StatusResponse{Status: true}, nil
}

// IsTranscribed checks if project is transcribed
//
//encore:api public method=GET path=/api/track-surfer/istranscribed
func IsTranscribed(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy status
	return &StatusResponse{Status: true}, nil
}

// IsAligned checks if project alignment is complete
//
//encore:api public method=GET path=/api/track-surfer/isaligned
func IsAligned(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy status
	return &StatusResponse{Status: false}, nil
}

// IsTranscoded checks if project is transcoded
//
//encore:api public method=GET path=/api/track-surfer/istranscoded
func IsTranscoded(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy status
	return &StatusResponse{Status: true}, nil
}

// IsAudioUploaded checks if audio is uploaded for a project
//
//encore:api public method=GET path=/api/track-surfer/isaudiouploaded
func IsAudioUploaded(ctx context.Context, req *GetCountRequest) (*StatusResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy status
	return &StatusResponse{Status: true}, nil
}

// Transcription Endpoints

// GetTranscription retrieves transcription for an audio file
//
//encore:api public method=GET path=/api/track-surfer/gettranscription
func GetTranscription(ctx context.Context, req *GetTranscriptionRequest) (*TranscriptionResponse, error) {
	if req.AudioFileID == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing audioFileID parameter",
		}
	}

	// Return dummy transcription
	return GetDummyTranscription(req.AudioFileID), nil
}

// DeleteTranscription deletes transcription for a project
//
//encore:api public method=DELETE path=/api/track-surfer/deletetranscription
func DeleteTranscription(ctx context.Context, req *GetCountRequest) (*DeleteResponse, error) {
	if req.PartitionKey == "" {
		return nil, &errs.Error{
			Code:    errs.InvalidArgument,
			Message: "Missing partitionKey parameter",
		}
	}

	// Return dummy deletion count
	return &DeleteResponse{Message: "150 words deleted"}, nil
}
