package tracksurfer_getapi

import "time"

// GetDummyUser returns dummy user data
func GetDummyUser(partitionKey string) *UserResponse {
	return &UserResponse{
		User: &User{
			PartitionKey: partitionKey,
			Row<PERSON>ey:       "user_001",
			Name:         "<PERSON>",
			Email:        "<EMAIL>",
			CreatedDate:  time.Now().AddDate(0, -6, 0),
			IsActive:     true,
		},
	}
}

// GetDummyUsers returns dummy users data
func GetDummyUsers() *UsersResponse {
	return &UsersResponse{
		Users: []User{
			{
				PartitionKey: "user_partition_1",
				<PERSON><PERSON><PERSON>:       "user_001",
				Name:         "<PERSON>",
				Email:        "<EMAIL>",
				CreatedDate:  time.Now().AddDate(0, -6, 0),
				IsActive:     true,
			},
			{
				PartitionKey: "user_partition_2",
				RowKey:       "user_002",
				Name:         "<PERSON>",
				Email:        "<EMAIL>",
				CreatedDate:  time.Now().AddDate(0, -3, 0),
				IsActive:     true,
			},
			{
				Partition<PERSON>ey: "user_partition_3",
				<PERSON><PERSON><PERSON>:       "user_003",
				Name:         "<PERSON>",
				Email:        "<EMAIL>",
				CreatedDate:  time.Now().AddDate(0, -1, 0),
				IsActive:     false,
			},
		},
	}
}

// GetDummyProject returns dummy project data
func GetDummyProject(rowKey string) *ProjectResponse {
	return &ProjectResponse{
		Project: &Project{
			PartitionKey:    "project_partition_1",
			RowKey:          rowKey,
			Name:            "Sample Audio Book Project",
			Description:     "A project for processing audio book files and creating transcriptions",
			CreatedDate:     time.Now().AddDate(0, -2, 0),
			LastModified:    time.Now().AddDate(0, 0, -5),
			Status:          "In Progress",
			AudioFileCount:  5,
			IsTextExtracted: true,
			IsTranscribed:   true,
			IsAligned:       false,
		},
	}
}

// GetDummyProjects returns dummy projects data
func GetDummyProjects() *ProjectsResponse {
	return &ProjectsResponse{
		Projects: []Project{
			{
				PartitionKey:    "project_partition_1",
				RowKey:          "project_001",
				Name:            "Audio Book Project 1",
				Description:     "First audio book processing project",
				CreatedDate:     time.Now().AddDate(0, -3, 0),
				LastModified:    time.Now().AddDate(0, 0, -2),
				Status:          "Completed",
				AudioFileCount:  8,
				IsTextExtracted: true,
				IsTranscribed:   true,
				IsAligned:       true,
			},
			{
				PartitionKey:    "project_partition_2",
				RowKey:          "project_002",
				Name:            "Audio Book Project 2",
				Description:     "Second audio book processing project",
				CreatedDate:     time.Now().AddDate(0, -2, 0),
				LastModified:    time.Now().AddDate(0, 0, -1),
				Status:          "In Progress",
				AudioFileCount:  5,
				IsTextExtracted: true,
				IsTranscribed:   false,
				IsAligned:       false,
			},
			{
				PartitionKey:    "project_partition_3",
				RowKey:          "project_003",
				Name:            "Audio Book Project 3",
				Description:     "Third audio book processing project",
				CreatedDate:     time.Now().AddDate(0, -1, 0),
				LastModified:    time.Now(),
				Status:          "Started",
				AudioFileCount:  3,
				IsTextExtracted: false,
				IsTranscribed:   false,
				IsAligned:       false,
			},
		},
	}
}

// GetDummyBook returns dummy book data
func GetDummyBook(rowKey string) *BookResponse {
	return &BookResponse{
		Book: &Book{
			RowKey:      rowKey,
			Title:       "The Great Adventure",
			Author:      "Sample Author",
			ISBN:        "978-0123456789",
			PublishDate: time.Date(2023, 1, 15, 0, 0, 0, 0, time.UTC),
			PageCount:   350,
			Language:    "English",
		},
	}
}

// GetDummyAudioFile returns dummy audio file data
func GetDummyAudioFile(partitionKey, rowKey string) *AudioFileResponse {
	return &AudioFileResponse{
		AudioFile: &AudioFile{
			PartitionKey: partitionKey,
			RowKey:       rowKey,
			FileName:     "chapter_01.mp3",
			FilePath:     "/audio/projects/" + partitionKey + "/chapter_01.mp3",
			Duration:     1800.5, // 30 minutes
			FileSize:     25600000, // ~25MB
			Format:       "MP3",
			UploadDate:   time.Now().AddDate(0, 0, -10),
			IsProcessed:  true,
		},
	}
}

// GetDummyAudioFiles returns dummy audio files data
func GetDummyAudioFiles(partitionKey string) *AudioFilesResponse {
	return &AudioFilesResponse{
		AudioFiles: []AudioFile{
			{
				PartitionKey: partitionKey,
				RowKey:       "audio_001",
				FileName:     "chapter_01.mp3",
				FilePath:     "/audio/projects/" + partitionKey + "/chapter_01.mp3",
				Duration:     1800.5,
				FileSize:     25600000,
				Format:       "MP3",
				UploadDate:   time.Now().AddDate(0, 0, -10),
				IsProcessed:  true,
			},
			{
				PartitionKey: partitionKey,
				RowKey:       "audio_002",
				FileName:     "chapter_02.mp3",
				FilePath:     "/audio/projects/" + partitionKey + "/chapter_02.mp3",
				Duration:     1650.3,
				FileSize:     23400000,
				Format:       "MP3",
				UploadDate:   time.Now().AddDate(0, 0, -9),
				IsProcessed:  true,
			},
			{
				PartitionKey: partitionKey,
				RowKey:       "audio_003",
				FileName:     "chapter_03.mp3",
				FilePath:     "/audio/projects/" + partitionKey + "/chapter_03.mp3",
				Duration:     1920.7,
				FileSize:     27200000,
				Format:       "MP3",
				UploadDate:   time.Now().AddDate(0, 0, -8),
				IsProcessed:  false,
			},
		},
	}
}

// GetDummyAudioFileList returns dummy audio file list
func GetDummyAudioFileList(partitionKey string) *AudioFileListResponse {
	return &AudioFileListResponse{
		FileNames: []string{
			"chapter_01.mp3",
			"chapter_02.mp3",
			"chapter_03.mp3",
			"chapter_04.mp3",
			"chapter_05.mp3",
		},
	}
}

// GetDummyAlignmentItems returns dummy alignment items
func GetDummyAlignmentItems(audioFileID, category string) *AlignmentItemsResponse {
	return &AlignmentItemsResponse{
		AlignmentItems: []AlignmentItem{
			{
				ID:          "align_001",
				AudioFileID: audioFileID,
				Category:    category,
				StartTime:   0.0,
				EndTime:     2.5,
				Text:        "In the beginning",
				Confidence:  0.95,
				IsConfirmed: true,
				CreatedDate: time.Now().AddDate(0, 0, -5),
			},
			{
				ID:          "align_002",
				AudioFileID: audioFileID,
				Category:    category,
				StartTime:   2.5,
				EndTime:     5.8,
				Text:        "there was a great adventure",
				Confidence:  0.92,
				IsConfirmed: false,
				CreatedDate: time.Now().AddDate(0, 0, -5),
			},
			{
				ID:          "align_003",
				AudioFileID: audioFileID,
				Category:    category,
				StartTime:   5.8,
				EndTime:     9.2,
				Text:        "waiting to unfold",
				Confidence:  0.88,
				IsConfirmed: true,
				CreatedDate: time.Now().AddDate(0, 0, -5),
			},
		},
	}
}

// GetDummyTranscription returns dummy transcription data
func GetDummyTranscription(audioFileID string) *TranscriptionResponse {
	return &TranscriptionResponse{
		Transcription: &Transcription{
			AudioFileID: audioFileID,
			Text:        "In the beginning there was a great adventure waiting to unfold. The story takes place in a magical land where anything is possible. Our hero embarks on a journey that will change their life forever.",
			Language:    "English",
			Confidence:  0.94,
			WordCount:   35,
			CreatedDate: time.Now().AddDate(0, 0, -3),
		},
	}
}
